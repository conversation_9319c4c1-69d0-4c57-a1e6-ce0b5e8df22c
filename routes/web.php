<?php

/**
 * Web Routes Configuration
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @version 1.0
 */

use SIU\MBGL\Core\Router;
use SIU\MBGL\Middleware\AuthMiddleware;
use SIU\MBGL\Middleware\AdminMiddleware;

// Public routes
$router->get('/', 'HomeController@index');
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->get('/logout', 'AuthController@logout');

// Password reset routes
$router->get('/forgot-password', 'AuthController@showForgotPassword');
$router->post('/forgot-password', 'AuthController@forgotPassword');
$router->get('/reset-password/{token}', 'AuthController@showResetPassword');
$router->post('/reset-password', 'AuthController@resetPassword');

// Protected routes - require authentication
$router->group(['middleware' => [AuthMiddleware::class]], function($router) {
    
    // Dashboard
    $router->get('/dashboard', 'DashboardController@index');
    
    // User profile
    $router->get('/profile', 'UserController@profile');
    $router->post('/profile', 'UserController@updateProfile');
    $router->post('/change-password', 'UserController@changePassword');
    
    // Data submission routes
    $router->get('/data/upload', 'DataController@showUpload');
    $router->post('/data/upload', 'DataController@upload');
    $router->get('/data/validate', 'DataController@validate');
    $router->get('/data/submissions', 'DataController@submissions');
    $router->get('/data/submission/{id}', 'DataController@viewSubmission');
    
    // Scoring and progress
    $router->get('/scoring', 'ScoringController@index');
    $router->get('/scoring/calculate/{id}', 'ScoringController@calculate');
    $router->get('/progress', 'ScoringController@progress');
    
    // Reports
    $router->get('/reports', 'ReportController@index');
    $router->get('/reports/generate', 'ReportController@generate');
    $router->get('/reports/export/{type}', 'ReportController@export');
    $router->get('/reports/view/{id}', 'ReportController@view');
});

// Admin routes - require admin role
$router->group(['prefix' => '/admin', 'middleware' => [AdminMiddleware::class]], function($router) {
    
    // Admin dashboard
    $router->get('/', 'AdminController@dashboard');
    
    // Institute management
    $router->get('/institutes', 'AdminController@institutes');
    $router->get('/institutes/create', 'AdminController@createInstitute');
    $router->post('/institutes', 'AdminController@storeInstitute');
    $router->get('/institutes/{id}/edit', 'AdminController@editInstitute');
    $router->post('/institutes/{id}', 'AdminController@updateInstitute');
    $router->delete('/institutes/{id}', 'AdminController@deleteInstitute');
    
    // User management
    $router->get('/users', 'AdminController@users');
    $router->get('/users/create', 'AdminController@createUser');
    $router->post('/users', 'AdminController@storeUser');
    $router->get('/users/{id}/edit', 'AdminController@editUser');
    $router->post('/users/{id}', 'AdminController@updateUser');
    $router->delete('/users/{id}', 'AdminController@deleteUser');
    
    // MBGL Configuration - New dedicated controller
    $router->get('/mbgl-config', 'MBGLConfigController@index');
    $router->get('/mbgl-config/export', 'MBGLConfigController@exportConfig');

    // Attributes management
    $router->get('/mbgl-config/attributes', 'MBGLConfigController@attributes');
    $router->get('/mbgl-config/create-attribute', 'MBGLConfigController@createAttribute');
    $router->post('/mbgl-config/create-attribute', 'MBGLConfigController@createAttribute');
    $router->get('/mbgl-config/edit-attribute', 'MBGLConfigController@editAttribute');
    $router->post('/mbgl-config/edit-attribute', 'MBGLConfigController@editAttribute');
    $router->post('/mbgl-config/delete-attribute', 'MBGLConfigController@deleteAttribute');

    // Sub-components management
    $router->get('/mbgl-config/sub-components', 'MBGLConfigController@subComponents');
    $router->get('/mbgl-config/create-sub-component', 'MBGLConfigController@createSubComponent');
    $router->post('/mbgl-config/create-sub-component', 'MBGLConfigController@createSubComponent');
    $router->get('/mbgl-config/edit-sub-component', 'MBGLConfigController@editSubComponent');
    $router->post('/mbgl-config/edit-sub-component', 'MBGLConfigController@editSubComponent');
    $router->post('/mbgl-config/delete-sub-component', 'MBGLConfigController@deleteSubComponent');

    // Validation rules management
    $router->get('/mbgl-config/validation-rules', 'MBGLConfigController@validationRules');
    $router->get('/mbgl-config/create-validation-rule', 'MBGLConfigController@createValidationRule');
    $router->post('/mbgl-config/create-validation-rule', 'MBGLConfigController@createValidationRule');
    $router->get('/mbgl-config/edit-validation-rule', 'MBGLConfigController@editValidationRule');
    $router->post('/mbgl-config/edit-validation-rule', 'MBGLConfigController@editValidationRule');
    $router->post('/mbgl-config/delete-validation-rule', 'MBGLConfigController@deleteValidationRule');

    // SDG Indicators management
    $router->get('/mbgl-config/sdg-indicators', 'MBGLConfigController@sdgIndicators');
    $router->get('/mbgl-config/create-sdg-indicator', 'MBGLConfigController@createSDGIndicator');
    $router->post('/mbgl-config/create-sdg-indicator', 'MBGLConfigController@createSDGIndicator');
    $router->get('/mbgl-config/edit-sdg-indicator', 'MBGLConfigController@editSDGIndicator');
    $router->post('/mbgl-config/edit-sdg-indicator', 'MBGLConfigController@editSDGIndicator');
    $router->post('/mbgl-config/delete-sdg-indicator', 'MBGLConfigController@deleteSDGIndicator');

    // Programme Codes management
    $router->get('/mbgl-config/programme-codes', 'MBGLConfigController@programmeCodes');
    $router->get('/mbgl-config/create-programme-code', 'MBGLConfigController@createProgrammeCode');
    $router->post('/mbgl-config/create-programme-code', 'MBGLConfigController@createProgrammeCode');
    $router->get('/mbgl-config/edit-programme-code', 'MBGLConfigController@editProgrammeCode');
    $router->post('/mbgl-config/edit-programme-code', 'MBGLConfigController@editProgrammeCode');
    $router->post('/mbgl-config/delete-programme-code', 'MBGLConfigController@deleteProgrammeCode');

    // Legacy MBGL routes (keeping for backward compatibility)
    $router->get('/mbgl', 'AdminController@mbglConfig');
    $router->get('/mbgl/attributes', 'AdminController@attributes');
    $router->post('/mbgl/attributes', 'AdminController@storeAttribute');
    $router->get('/mbgl/attributes/{id}/edit', 'AdminController@editAttribute');
    $router->post('/mbgl/attributes/{id}', 'AdminController@updateAttribute');

    // Legacy Sub-components
    $router->get('/mbgl/sub-components', 'AdminController@subComponents');
    $router->post('/mbgl/sub-components', 'AdminController@storeSubComponent');
    $router->get('/mbgl/sub-components/{id}/edit', 'AdminController@editSubComponent');
    $router->post('/mbgl/sub-components/{id}', 'AdminController@updateSubComponent');

    // Legacy Validation rules
    $router->get('/validation-rules', 'AdminController@validationRules');
    $router->post('/validation-rules', 'AdminController@storeValidationRule');
    $router->get('/validation-rules/{id}/edit', 'AdminController@editValidationRule');
    $router->post('/validation-rules/{id}', 'AdminController@updateValidationRule');
    
    // CSV Templates
    $router->get('/csv-templates', 'AdminController@csvTemplates');
    $router->get('/csv-templates/generate', 'AdminController@generateTemplate');
    $router->get('/csv-templates/download/{faculty}', 'AdminController@downloadTemplate');
    
    // System settings
    $router->get('/settings', 'AdminController@settings');
    $router->post('/settings', 'AdminController@updateSettings');
    
    // Activity logs
    $router->get('/logs', 'AdminController@logs');
    $router->get('/logs/security', 'AdminController@securityLogs');
    
    // System health
    $router->get('/health', 'AdminController@systemHealth');
    $router->get('/backup', 'AdminController@backup');
});

// API routes for AJAX requests
$router->group(['prefix' => '/api', 'middleware' => [AuthMiddleware::class]], function($router) {
    
    // Institute data
    $router->get('/institutes', 'ApiController@institutes');
    $router->get('/institutes/{id}', 'ApiController@institute');
    
    // User data
    $router->get('/users', 'ApiController@users');
    $router->get('/users/{id}', 'ApiController@user');
    
    // MBGL data
    $router->get('/attributes', 'ApiController@attributes');
    $router->get('/sub-components', 'ApiController@subComponents');
    $router->get('/validation-rules', 'ApiController@validationRules');
    
    // CSV validation
    $router->post('/validate-csv', 'ApiController@validateCSV');
    $router->post('/process-csv', 'ApiController@processCSV');
    
    // Scoring
    $router->get('/scores/{institute_id}', 'ApiController@scores');
    $router->post('/calculate-scores', 'ApiController@calculateScores');
    
    // Reports
    $router->get('/reports/data', 'ApiController@reportData');
    $router->post('/reports/generate', 'ApiController@generateReport');
});

// File download routes
$router->get('/download/template/{faculty}', 'DownloadController@template');
$router->get('/download/report/{id}', 'DownloadController@report');
$router->get('/download/evidence/{id}', 'DownloadController@evidence');

// Error pages
$router->get('/403', function() {
    http_response_code(403);
    require_once __DIR__ . '/../src/Views/errors/403.php';
});

$router->get('/404', function() {
    http_response_code(404);
    require_once __DIR__ . '/../src/Views/errors/404.php';
});

$router->get('/500', function() {
    http_response_code(500);
    require_once __DIR__ . '/../src/Views/errors/500.php';
});
