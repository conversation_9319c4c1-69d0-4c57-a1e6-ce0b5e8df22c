<?php

/**
 * Advanced Validation Rules Engine Test
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Comprehensive test suite for the validation engine
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @version 1.0
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

use SIU\MBGL\Core\CSVValidator;
use SIU\MBGL\Core\CSVParser;
use SIU\MBGL\Models\SubComponent;

echo "=== Advanced Validation Rules Engine Test ===\n\n";

try {
    // Initialize components
    $csvValidator = new CSVValidator();
    $csvParser = new CSVParser();
    $subComponentModel = new SubComponent();
    
    // Get a sub-component for testing (A5 - Faculty and Staff)
    $subComponents = $subComponentModel->findAll(['status' => 'active']);
    $testSubComponent = null;
    
    foreach ($subComponents as $sc) {
        if (strpos($sc['component_code'], 'A5') === 0) {
            $testSubComponent = $sc;
            break;
        }
    }
    
    if (!$testSubComponent) {
        echo "No A5 sub-component found for testing.\n";
        exit(1);
    }
    
    echo "Testing with Sub-Component: {$testSubComponent['component_code']} - {$testSubComponent['component_name']}\n\n";
    
    // Test 1: CSV Parser Test
    echo "=== Test 1: CSV Parser ===\n";
    
    $testCSVContent = "institute_code,academic_year,faculty_name,designation,qualification,experience_years,email\n";
    $testCSVContent .= "SIU001,2023-24,Dr. John Smith,Professor,PhD,15,<EMAIL>\n";
    $testCSVContent .= "SIU002,2023-24,Ms. Jane Doe,Assistant Professor,MBA,5,<EMAIL>\n";
    $testCSVContent .= "INVALID,2023,Invalid Name123,Invalid Designation,Invalid Qual,999,<EMAIL>\n";
    
    $parseResult = $csvParser->parseString($testCSVContent);
    
    if ($parseResult['success']) {
        echo "✅ CSV parsing successful\n";
        echo "   Rows parsed: " . count($parseResult['data']) . "\n";
        echo "   Columns: " . implode(', ', array_keys($parseResult['data'][0])) . "\n";
        
        if (!empty($parseResult['warnings'])) {
            echo "   Warnings: " . count($parseResult['warnings']) . "\n";
        }
    } else {
        echo "❌ CSV parsing failed: " . $parseResult['message'] . "\n";
    }
    
    echo "\n";
    
    // Test 2: Validation Rules Test
    echo "=== Test 2: Validation Rules Test ===\n";
    
    if ($parseResult['success']) {
        $validationResult = $csvValidator->validateCSVData(
            $testSubComponent['id'], 
            $parseResult['data'],
            ['institute_id' => 1] // Assuming institute ID 1 exists
        );
        
        echo "Validation Result:\n";
        echo "  Valid: " . ($validationResult['valid'] ? 'Yes' : 'No') . "\n";
        echo "  Message: " . $validationResult['message'] . "\n";
        
        if (!empty($validationResult['errors'])) {
            echo "  Errors (" . count($validationResult['errors']) . "):\n";
            foreach ($validationResult['errors'] as $error) {
                if (isset($error['row'])) {
                    echo "    Row {$error['row']}, Column {$error['column']}: {$error['message']}\n";
                } else {
                    echo "    {$error['type']}: {$error['message']}\n";
                }
            }
        }
        
        if (!empty($validationResult['warnings'])) {
            echo "  Warnings (" . count($validationResult['warnings']) . "):\n";
            foreach ($validationResult['warnings'] as $warning) {
                echo "    {$warning['type']}: {$warning['message']}\n";
            }
        }
        
        if (!empty($validationResult['statistics'])) {
            echo "  Statistics:\n";
            foreach ($validationResult['statistics'] as $key => $value) {
                echo "    {$key}: {$value}\n";
            }
        }
    }
    
    echo "\n";
    
    // Test 3: Specific Validation Types Test
    echo "=== Test 3: Specific Validation Types Test ===\n";
    
    $validationTests = [
        [
            'name' => 'Email Validation',
            'data' => [
                ['institute_code' => 'SIU001', 'academic_year' => '2023-24', 'email' => '<EMAIL>'],
                ['institute_code' => 'SIU001', 'academic_year' => '2023-24', 'email' => '<EMAIL>']
            ]
        ],
        [
            'name' => 'Academic Year Validation',
            'data' => [
                ['institute_code' => 'SIU001', 'academic_year' => '2023-24'],
                ['institute_code' => 'SIU001', 'academic_year' => '2023'],
                ['institute_code' => 'SIU001', 'academic_year' => 'invalid']
            ]
        ],
        [
            'name' => 'Range Validation',
            'data' => [
                ['institute_code' => 'SIU001', 'academic_year' => '2023-24', 'experience_years' => '10'],
                ['institute_code' => 'SIU001', 'academic_year' => '2023-24', 'experience_years' => '999'],
                ['institute_code' => 'SIU001', 'academic_year' => '2023-24', 'experience_years' => '-5']
            ]
        ]
    ];
    
    foreach ($validationTests as $test) {
        echo "Testing: {$test['name']}\n";
        
        $result = $csvValidator->validateCSVData($testSubComponent['id'], $test['data']);
        
        echo "  Valid: " . ($result['valid'] ? 'Yes' : 'No') . "\n";
        echo "  Errors: " . count($result['errors']) . "\n";
        
        if (!empty($result['errors'])) {
            foreach ($result['errors'] as $error) {
                if (isset($error['row'])) {
                    echo "    Row {$error['row']}: {$error['message']}\n";
                }
            }
        }
        
        echo "\n";
    }
    
    // Test 4: Performance Test
    echo "=== Test 4: Performance Test ===\n";
    
    // Generate larger dataset
    $largeCSVData = [];
    $headers = ['institute_code', 'academic_year', 'faculty_name', 'designation', 'qualification', 'experience_years', 'email'];
    
    for ($i = 1; $i <= 1000; $i++) {
        $largeCSVData[] = [
            'institute_code' => 'SIU001',
            'academic_year' => '2023-24',
            'faculty_name' => "Faculty Member {$i}",
            'designation' => 'Assistant Professor',
            'qualification' => 'PhD',
            'experience_years' => rand(1, 30),
            'email' => "faculty{$i}@siu.edu.in"
        ];
    }
    
    $startTime = microtime(true);
    $performanceResult = $csvValidator->validateCSVData($testSubComponent['id'], $largeCSVData);
    $endTime = microtime(true);
    
    $executionTime = round(($endTime - $startTime) * 1000, 2);
    
    echo "Performance Test Results:\n";
    echo "  Records validated: " . count($largeCSVData) . "\n";
    echo "  Execution time: {$executionTime} ms\n";
    echo "  Records per second: " . round(count($largeCSVData) / (($endTime - $startTime)), 2) . "\n";
    echo "  Valid: " . ($performanceResult['valid'] ? 'Yes' : 'No') . "\n";
    echo "  Errors: " . count($performanceResult['errors']) . "\n";
    
    echo "\n";
    
    // Test 5: Complex Validation Scenarios
    echo "=== Test 5: Complex Validation Scenarios ===\n";
    
    $complexData = [
        // Valid record
        [
            'institute_code' => 'SIU001',
            'academic_year' => '2023-24',
            'faculty_name' => 'Dr. Rajesh Kumar',
            'designation' => 'Professor',
            'qualification' => 'PhD',
            'experience_years' => '20',
            'email' => '<EMAIL>'
        ],
        // Multiple validation errors
        [
            'institute_code' => 'INVALID_CODE',
            'academic_year' => 'INVALID_YEAR',
            'faculty_name' => 'Invalid123Name',
            'designation' => 'Invalid Designation',
            'qualification' => 'Invalid Qualification',
            'experience_years' => '999',
            'email' => 'invalid-email'
        ],
        // Missing required fields
        [
            'institute_code' => '',
            'academic_year' => '',
            'faculty_name' => '',
            'designation' => '',
            'qualification' => '',
            'experience_years' => '',
            'email' => ''
        ]
    ];
    
    $complexResult = $csvValidator->validateCSVData($testSubComponent['id'], $complexData);
    
    echo "Complex Validation Results:\n";
    echo "  Total records: " . count($complexData) . "\n";
    echo "  Valid: " . ($complexResult['valid'] ? 'Yes' : 'No') . "\n";
    echo "  Total errors: " . count($complexResult['errors']) . "\n";
    echo "  Total warnings: " . count($complexResult['warnings']) . "\n";
    
    if (!empty($complexResult['statistics'])) {
        echo "  Success rate: " . round($complexResult['statistics']['success_rate'], 2) . "%\n";
    }
    
    // Group errors by type
    $errorsByType = [];
    foreach ($complexResult['errors'] as $error) {
        $type = $error['type'] ?? 'field_error';
        $errorsByType[$type] = ($errorsByType[$type] ?? 0) + 1;
    }
    
    echo "  Error breakdown:\n";
    foreach ($errorsByType as $type => $count) {
        echo "    {$type}: {$count}\n";
    }
    
    echo "\n=== Test Summary ===\n";
    echo "✅ CSV Parser: Working correctly\n";
    echo "✅ Validation Engine: Functional with comprehensive error reporting\n";
    echo "✅ Performance: Capable of handling large datasets efficiently\n";
    echo "✅ Complex Scenarios: Handles multiple validation types and error conditions\n";
    echo "✅ Error Reporting: Detailed error messages with row and column information\n";
    
    echo "\nAdvanced Validation Rules Engine test completed successfully! 🎉\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
