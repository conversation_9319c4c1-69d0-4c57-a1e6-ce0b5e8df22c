<?php

namespace SIU\MBGL\Controllers;

use SIU\MBGL\Core\Controller;
use SIU\MBGL\Models\User;
use SIU\MBGL\Models\Institute;

/**
 * Dashboard Controller
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
class DashboardController extends Controller
{
    private User $userModel;
    private Institute $instituteModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        $this->instituteModel = new Institute();
    }
    
    public function index(): void
    {
        $this->requireAuth();
        
        $user = $this->session->getUser();
        
        // Get dashboard data based on user role
        $dashboardData = $this->getDashboardData($user);
        
        $this->view('dashboard/index', [
            'title' => 'Dashboard - MBGL AQAR System',
            'user' => $user,
            'data' => $dashboardData
        ]);
    }
    
    private function getDashboardData(array $user): array
    {
        $data = [
            'stats' => [],
            'recent_activities' => [],
            'notifications' => [],
            'quick_actions' => []
        ];
        
        switch ($user['role']) {
            case 'Super Admin':
                $data = $this->getSuperAdminDashboard($user);
                break;
                
            case 'NAAC Coordinator':
                $data = $this->getNAACCoordinatorDashboard($user);
                break;
                
            case 'Quality Assurance Officer':
                $data = $this->getQAOfficerDashboard($user);
                break;
                
            case 'Academic Administrator':
                $data = $this->getAcademicAdminDashboard($user);
                break;
        }
        
        return $data;
    }
    
    private function getSuperAdminDashboard(array $user): array
    {
        // Get system-wide statistics
        $totalInstitutes = $this->instituteModel->count(['status' => 'active']);
        $totalUsers = $this->userModel->count(['status' => 'active']);
        
        // Get faculty statistics
        $facultyStats = $this->instituteModel->getFacultyStats();
        
        // Get recent activities
        $recentActivities = $this->db->fetchAll(
            "SELECT al.*, u.name as user_name, i.institute_short_name 
             FROM activity_logs al 
             LEFT JOIN users u ON al.user_id = u.id 
             LEFT JOIN institutes i ON u.institute_id = i.id 
             ORDER BY al.created_at DESC 
             LIMIT 10"
        );
        
        return [
            'stats' => [
                'total_institutes' => $totalInstitutes,
                'total_users' => $totalUsers,
                'faculty_stats' => $facultyStats,
                'active_submissions' => 0, // TODO: Implement when submission system is ready
                'pending_approvals' => 0   // TODO: Implement when approval system is ready
            ],
            'recent_activities' => $recentActivities,
            'notifications' => [
                ['type' => 'info', 'message' => 'System is running normally'],
                ['type' => 'warning', 'message' => 'Database backup scheduled for tonight']
            ],
            'quick_actions' => [
                ['title' => 'Manage Institutes', 'url' => '/admin/institutes', 'icon' => 'building'],
                ['title' => 'Manage Users', 'url' => '/admin/users', 'icon' => 'users'],
                ['title' => 'MBGL Configuration', 'url' => '/admin/mbgl', 'icon' => 'settings'],
                ['title' => 'System Reports', 'url' => '/admin/reports', 'icon' => 'chart-bar']
            ]
        ];
    }
    
    private function getNAACCoordinatorDashboard(array $user): array
    {
        $instituteId = $user['institute_id'];
        
        // Get institute-specific statistics
        $institute = $this->instituteModel->find($instituteId);
        $instituteUsers = $this->instituteModel->getUsers($instituteId);
        
        // Get recent submissions for this institute
        $recentSubmissions = $this->instituteModel->getSubmissions($instituteId);
        
        return [
            'stats' => [
                'institute_name' => $institute['institute_name'] ?? 'Unknown',
                'faculty' => $institute['faculty'] ?? 'Unknown',
                'total_users' => count($instituteUsers),
                'pending_submissions' => count(array_filter($recentSubmissions, fn($s) => $s['submission_status'] === 'draft')),
                'completed_submissions' => count(array_filter($recentSubmissions, fn($s) => $s['submission_status'] === 'submitted'))
            ],
            'recent_activities' => $this->userModel->getActivityLogs($user['id'], 10),
            'notifications' => [
                ['type' => 'info', 'message' => 'Welcome to MBGL AQAR Management System'],
                ['type' => 'reminder', 'message' => 'Please review pending data submissions']
            ],
            'quick_actions' => [
                ['title' => 'Upload Data', 'url' => '/data/upload', 'icon' => 'upload'],
                ['title' => 'View Submissions', 'url' => '/data/submissions', 'icon' => 'list'],
                ['title' => 'Generate Reports', 'url' => '/reports', 'icon' => 'chart-bar'],
                ['title' => 'View Progress', 'url' => '/progress', 'icon' => 'progress']
            ]
        ];
    }
    
    private function getQAOfficerDashboard(array $user): array
    {
        $instituteId = $user['institute_id'];
        
        // Get institute information
        $institute = $this->instituteModel->find($instituteId);
        
        // Get user's submissions
        $userSubmissions = $this->userModel->getSubmissions($user['id']);
        
        return [
            'stats' => [
                'institute_name' => $institute['institute_name'] ?? 'Unknown',
                'faculty' => $institute['faculty'] ?? 'Unknown',
                'my_submissions' => count($userSubmissions),
                'pending_submissions' => count(array_filter($userSubmissions, fn($s) => $s['submission_status'] === 'draft')),
                'approved_submissions' => count(array_filter($userSubmissions, fn($s) => $s['submission_status'] === 'approved'))
            ],
            'recent_activities' => $this->userModel->getActivityLogs($user['id'], 10),
            'notifications' => [
                ['type' => 'info', 'message' => 'Welcome to MBGL AQAR Management System'],
                ['type' => 'task', 'message' => 'You have data entry tasks pending']
            ],
            'quick_actions' => [
                ['title' => 'Upload Data', 'url' => '/data/upload', 'icon' => 'upload'],
                ['title' => 'My Submissions', 'url' => '/data/submissions', 'icon' => 'list'],
                ['title' => 'Data Validation', 'url' => '/data/validate', 'icon' => 'check-circle'],
                ['title' => 'Help & Guidelines', 'url' => '/help', 'icon' => 'question-circle']
            ]
        ];
    }
    
    private function getAcademicAdminDashboard(array $user): array
    {
        $instituteId = $user['institute_id'];
        
        // Get institute information
        $institute = $this->instituteModel->find($instituteId);
        
        return [
            'stats' => [
                'institute_name' => $institute['institute_name'] ?? 'Unknown',
                'faculty' => $institute['faculty'] ?? 'Unknown',
                'administrative_tasks' => 0, // TODO: Implement when admin tasks are ready
                'pending_reviews' => 0       // TODO: Implement when review system is ready
            ],
            'recent_activities' => $this->userModel->getActivityLogs($user['id'], 10),
            'notifications' => [
                ['type' => 'info', 'message' => 'Welcome to MBGL AQAR Management System'],
                ['type' => 'admin', 'message' => 'Administrative functions are being prepared']
            ],
            'quick_actions' => [
                ['title' => 'Administrative Data', 'url' => '/admin-data', 'icon' => 'database'],
                ['title' => 'Review Submissions', 'url' => '/review', 'icon' => 'eye'],
                ['title' => 'Generate Reports', 'url' => '/reports', 'icon' => 'chart-bar'],
                ['title' => 'System Settings', 'url' => '/settings', 'icon' => 'cog']
            ]
        ];
    }
}
