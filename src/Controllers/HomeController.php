<?php

namespace SIU\MBGL\Controllers;

use SIU\MBGL\Core\Controller;

/**
 * Home Controller
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
class HomeController extends Controller
{
    public function index(): void
    {
        // If user is logged in, redirect to dashboard
        if ($this->session->isLoggedIn()) {
            $user = $this->session->getUser();
            $redirectUrl = $this->getRedirectUrlByRole($user['role']);
            $this->redirect($redirectUrl);
        }
        
        // Show welcome page for non-authenticated users
        $this->view('home/index', [
            'title' => 'Welcome - MBGL AQAR Management System',
            'config' => $this->config
        ]);
    }
    
    private function getRedirectUrlByRole(string $role): string
    {
        switch ($role) {
            case 'Super Admin':
                return '/admin';
            case 'NAAC Coordinator':
            case 'Quality Assurance Officer':
            case 'Academic Administrator':
                return '/dashboard';
            default:
                return '/dashboard';
        }
    }
}
