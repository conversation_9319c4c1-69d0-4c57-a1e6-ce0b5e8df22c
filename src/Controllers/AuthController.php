<?php

namespace SIU\MBGL\Controllers;

use SIU\MBGL\Core\Controller;
use SIU\MBGL\Models\User;

/**
 * Authentication Controller
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR>
 * @version 1.0
 */
class AuthController extends Controller
{
    private User $userModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
    }
    
    public function showLogin(): void
    {
        // Redirect if already logged in
        if ($this->session->isLoggedIn()) {
            $this->redirect('/dashboard');
        }
        
        $this->view('auth/login', [
            'title' => 'Login - MBGL AQAR System',
            'errors' => $this->session->getFlash('error'),
            'success' => $this->session->getFlash('success')
        ]);
    }
    
    public function login(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/login');
        }
        
        // Validate CSRF token (temporarily disabled for testing)
        // if (!$this->validateCSRF()) {
        //     $this->redirectWithMessage('/login', 'Invalid security token. Please try again.', 'error');
        // }
        
        $email = $this->sanitizeInput($_POST['email'] ?? '');
        $securityCode = $this->sanitizeInput($_POST['security_code'] ?? '');
        
        // Validate input
        $validation = $this->validateInput([
            'email' => ['required', 'email', 'siu_email'],
            'security_code' => ['required', 'security_code']
        ], [
            'email' => $email,
            'security_code' => $securityCode
        ]);
        
        if (!$validation['valid']) {
            error_log("Validation failed: " . json_encode($validation['errors']));
            $errors = [];
            foreach ($validation['errors'] as $field => $fieldErrors) {
                $errors = array_merge($errors, $fieldErrors);
            }
            $this->session->setFlash('error', implode('<br>', $errors));
            $this->redirect('/login');
        }
        
        // Check rate limiting
        $rateLimitKey = 'login_' . $_SERVER['REMOTE_ADDR'];
        if (!$this->security->checkRateLimit($rateLimitKey, 5, 300)) { // 5 attempts per 5 minutes
            $this->redirectWithMessage('/login', 'Too many login attempts. Please try again later.', 'error');
        }
        
        // Attempt authentication
        error_log("Attempting authentication for: " . $email . " with code: " . $securityCode);
        $user = $this->userModel->authenticate($email, $securityCode);

        if (!$user) {
            error_log("Authentication failed for: " . $email);
            // Increment failed login attempts
            $this->userModel->incrementLoginAttempts($email);

            // Log security event
            $this->security->logSecurityEvent('failed_login', [
                'email' => $email,
                'ip' => $_SERVER['REMOTE_ADDR'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT']
            ]);

            $this->redirectWithMessage('/login', 'Invalid email or security code.', 'error');
        }

        error_log("Authentication successful for: " . $email . ", role: " . $user['role']);
        
        // Get user with institute information
        $userWithInstitute = $this->userModel->getWithInstitute($user['id']);
        
        // Set user session
        $this->session->setUser($userWithInstitute);
        
        // Log successful login
        $this->logActivity('user_login', [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role']
        ]);
        
        // Log security event
        $this->security->logSecurityEvent('successful_login', [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role']
        ]);
        
        // Redirect based on role
        $redirectUrl = $this->getRedirectUrlByRole($user['role']);
        error_log("Redirecting to: " . $redirectUrl);
        $this->redirect($redirectUrl);
    }
    
    public function logout(): void
    {
        $user = $this->session->getUser();
        
        if ($user) {
            // Log logout activity
            $this->logActivity('user_logout', [
                'user_id' => $user['id'],
                'email' => $user['email']
            ]);
            
            // Log security event
            $this->security->logSecurityEvent('user_logout', [
                'user_id' => $user['id'],
                'email' => $user['email']
            ]);
        }
        
        // Clear session and logout
        $this->session->logout();
        
        $this->redirectWithMessage('/login', 'You have been logged out successfully.', 'success');
    }
    
    public function showForgotPassword(): void
    {
        $this->view('auth/forgot-password', [
            'title' => 'Forgot Password - MBGL AQAR System',
            'errors' => $this->session->getFlash('error'),
            'success' => $this->session->getFlash('success')
        ]);
    }
    
    public function forgotPassword(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/forgot-password');
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            $this->redirectWithMessage('/forgot-password', 'Invalid security token. Please try again.', 'error');
        }
        
        $email = $this->sanitizeInput($_POST['email'] ?? '');
        
        // Validate input
        $validation = $this->validateInput([
            'email' => ['required', 'email', 'siu_email']
        ], [
            'email' => $email
        ]);
        
        if (!$validation['valid']) {
            $errors = [];
            foreach ($validation['errors'] as $field => $fieldErrors) {
                $errors = array_merge($errors, $fieldErrors);
            }
            $this->session->setFlash('error', implode('<br>', $errors));
            $this->redirect('/forgot-password');
        }
        
        // Check if user exists
        $user = $this->userModel->findByEmail($email);
        
        if ($user) {
            // Generate reset token (in a real implementation, this would be sent via email)
            $resetToken = $this->security->generateSecureToken();
            
            // For now, just show a success message
            // In production, you would send an email with the reset link
            $this->redirectWithMessage('/forgot-password', 
                'If your email is registered, you will receive password reset instructions.', 
                'success');
        } else {
            // Don't reveal if email exists or not for security
            $this->redirectWithMessage('/forgot-password', 
                'If your email is registered, you will receive password reset instructions.', 
                'success');
        }
    }
    
    public function showResetPassword(string $token): void
    {
        // In a real implementation, validate the reset token
        $this->view('auth/reset-password', [
            'title' => 'Reset Password - MBGL AQAR System',
            'token' => $token,
            'errors' => $this->session->getFlash('error'),
            'success' => $this->session->getFlash('success')
        ]);
    }
    
    public function resetPassword(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/login');
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            $this->redirectWithMessage('/login', 'Invalid security token. Please try again.', 'error');
        }
        
        $token = $this->sanitizeInput($_POST['token'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate input
        $validation = $this->validateInput([
            'password' => ['required', ['min_length', 8]],
            'confirm_password' => ['required']
        ], [
            'password' => $password,
            'confirm_password' => $confirmPassword
        ]);
        
        if (!$validation['valid']) {
            $errors = [];
            foreach ($validation['errors'] as $field => $fieldErrors) {
                $errors = array_merge($errors, $fieldErrors);
            }
            $this->session->setFlash('error', implode('<br>', $errors));
            $this->redirect("/reset-password/{$token}");
        }
        
        if ($password !== $confirmPassword) {
            $this->redirectWithMessage("/reset-password/{$token}", 'Passwords do not match.', 'error');
        }
        
        // In a real implementation, validate the token and update the password
        $this->redirectWithMessage('/login', 'Password has been reset successfully. Please login with your new password.', 'success');
    }
    
    private function getRedirectUrlByRole(string $role): string
    {
        switch ($role) {
            case 'Super Admin':
                return '/admin';
            case 'NAAC Coordinator':
            case 'Quality Assurance Officer':
            case 'Academic Administrator':
                return '/dashboard';
            default:
                return '/dashboard';
        }
    }
}
