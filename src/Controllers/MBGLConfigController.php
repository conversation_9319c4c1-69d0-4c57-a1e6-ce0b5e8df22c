<?php

namespace SIU\MBGL\Controllers;

use SIU\MBGL\Core\Controller;
use SIU\MBGL\Core\Session;
use SIU\MBGL\Core\Validator;
use SIU\MBGL\Models\MBGLAttribute;
use SIU\MBGL\Models\SubComponent;
use SIU\MBGL\Models\ValidationRule;
use SIU\MBGL\Models\SDGIndicator;
use SIU\MBGL\Models\ProgrammeCode;

/**
 * MBGL Configuration Controller
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Manages MBGL framework configuration including attributes, sub-components,
 * validation rules, SDG indicators, and programme codes
 * 
 * <AUTHOR> Dharmendra Pandey
 * @version 1.0
 */
class MBGLConfigController extends Controller
{
    private MBGLAttribute $attributeModel;
    private SubComponent $subComponentModel;
    private ValidationRule $validationRuleModel;
    private SDGIndicator $sdgIndicatorModel;
    private ProgrammeCode $programmeCodeModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->attributeModel = new MBGLAttribute();
        $this->subComponentModel = new SubComponent();
        $this->validationRuleModel = new ValidationRule();
        $this->sdgIndicatorModel = new SDGIndicator();
        $this->programmeCodeModel = new ProgrammeCode();
    }
    
    /**
     * Display MBGL configuration dashboard
     */
    public function index(): void
    {
        $this->requireAuth(['Super Admin', 'NAAC Coordinator']);
        
        try {
            $data = [
                'page_title' => 'MBGL Framework Configuration',
                'attributes' => $this->attributeModel->getAttributesWithSubComponents(),
                'attribute_stats' => $this->attributeModel->getAttributeStats(),
                'sub_component_stats' => $this->subComponentModel->getSubComponentStats(),
                'validation_stats' => $this->validationRuleModel->getValidationStats(),
                'sdg_count' => $this->sdgIndicatorModel->count(['status' => 'active']),
                'programme_count' => $this->programmeCodeModel->count(['status' => 'active']),
                'total_max_points' => $this->attributeModel->getTotalMaxPoints()
            ];
            
            $this->render('mbgl-config/index', $data);
            
        } catch (\Exception $e) {
            $this->handleError('Failed to load MBGL configuration', $e);
        }
    }
    
    /**
     * Display attributes management
     */
    public function attributes(): void
    {
        $this->requireAuth(['Super Admin', 'NAAC Coordinator']);
        
        try {
            $page = (int)($_GET['page'] ?? 1);
            $search = $_GET['search'] ?? '';
            
            if ($search) {
                $attributes = $this->attributeModel->search($search);
                $pagination = null;
            } else {
                $result = $this->attributeModel->paginate($page, 15, ['status' => 'active'], 'sort_order ASC');
                $attributes = $result['data'];
                $pagination = $result['pagination'];
            }
            
            $data = [
                'page_title' => 'MBGL Attributes Management',
                'attributes' => $attributes,
                'pagination' => $pagination,
                'search' => $search,
                'attribute_types' => ['INPUT', 'PROCESS', 'OUTCOME', 'EXTENDED'],
                'type_distribution' => $this->attributeModel->getTypeDistribution()
            ];
            
            $this->render('mbgl-config/attributes', $data);
            
        } catch (\Exception $e) {
            $this->handleError('Failed to load attributes', $e);
        }
    }
    
    /**
     * Create new attribute
     */
    public function createAttribute(): void
    {
        $this->requireAuth(['Super Admin']);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $validator = new Validator($_POST);
                $validator->required(['attribute_code', 'attribute_name', 'attribute_type', 'max_points'])
                         ->string(['attribute_code', 'attribute_name', 'attribute_type', 'description'])
                         ->integer(['max_points', 'sort_order'])
                         ->in('attribute_type', ['INPUT', 'PROCESS', 'OUTCOME', 'EXTENDED']);
                
                if (!$validator->isValid()) {
                    throw new \Exception('Validation failed: ' . implode(', ', $validator->getErrors()));
                }
                
                $data = $validator->getData();
                
                // Validate unique code
                if (!$this->attributeModel->validateUniqueCode($data['attribute_code'])) {
                    throw new \Exception('Attribute code already exists');
                }
                
                // Set default sort order if not provided
                if (empty($data['sort_order'])) {
                    $data['sort_order'] = $this->attributeModel->getNextSortOrder();
                }
                
                $data['status'] = 'active';
                
                $attributeId = $this->attributeModel->create($data);
                
                Session::setFlash('success', 'Attribute created successfully');
                $this->redirect('/mbgl-config/attributes');
                
            } catch (\Exception $e) {
                Session::setFlash('error', $e->getMessage());
                $this->redirect('/mbgl-config/attributes');
            }
        }
        
        $data = [
            'page_title' => 'Create MBGL Attribute',
            'attribute_types' => ['INPUT', 'PROCESS', 'OUTCOME', 'EXTENDED'],
            'next_sort_order' => $this->attributeModel->getNextSortOrder()
        ];
        
        $this->render('mbgl-config/create-attribute', $data);
    }
    
    /**
     * Edit attribute
     */
    public function editAttribute(): void
    {
        $this->requireAuth(['Super Admin']);
        
        $id = (int)($_GET['id'] ?? 0);
        $attribute = $this->attributeModel->find($id);
        
        if (!$attribute) {
            Session::setFlash('error', 'Attribute not found');
            $this->redirect('/mbgl-config/attributes');
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $validator = new Validator($_POST);
                $validator->required(['attribute_code', 'attribute_name', 'attribute_type', 'max_points'])
                         ->string(['attribute_code', 'attribute_name', 'attribute_type', 'description'])
                         ->integer(['max_points', 'sort_order'])
                         ->in('attribute_type', ['INPUT', 'PROCESS', 'OUTCOME', 'EXTENDED']);
                
                if (!$validator->isValid()) {
                    throw new \Exception('Validation failed: ' . implode(', ', $validator->getErrors()));
                }
                
                $data = $validator->getData();
                
                // Validate unique code (excluding current record)
                if (!$this->attributeModel->validateUniqueCode($data['attribute_code'], $id)) {
                    throw new \Exception('Attribute code already exists');
                }
                
                $this->attributeModel->update($id, $data);
                
                Session::setFlash('success', 'Attribute updated successfully');
                $this->redirect('/mbgl-config/attributes');
                
            } catch (\Exception $e) {
                Session::setFlash('error', $e->getMessage());
            }
        }
        
        $data = [
            'page_title' => 'Edit MBGL Attribute',
            'attribute' => $attribute,
            'attribute_types' => ['INPUT', 'PROCESS', 'OUTCOME', 'EXTENDED']
        ];
        
        $this->render('mbgl-config/edit-attribute', $data);
    }
    
    /**
     * Display sub-components management
     */
    public function subComponents(): void
    {
        $this->requireAuth(['Super Admin', 'NAAC Coordinator']);
        
        try {
            $page = (int)($_GET['page'] ?? 1);
            $search = $_GET['search'] ?? '';
            $attributeId = (int)($_GET['attribute_id'] ?? 0);
            
            $conditions = ['status' => 'active'];
            if ($attributeId) {
                $conditions['attribute_id'] = $attributeId;
            }
            
            if ($search) {
                $subComponents = $this->subComponentModel->search($search, $attributeId ?: null);
                $pagination = null;
            } else {
                $result = $this->subComponentModel->paginate($page, 15, $conditions, 'sort_order ASC');
                $subComponents = $result['data'];
                $pagination = $result['pagination'];
            }
            
            // Get sub-components with attribute details
            $subComponentsWithDetails = $this->subComponentModel->getSubComponentsWithValidationRules($attributeId ?: null);
            
            $data = [
                'page_title' => 'Sub-Components Management',
                'sub_components' => $subComponentsWithDetails,
                'pagination' => $pagination,
                'search' => $search,
                'selected_attribute_id' => $attributeId,
                'attributes' => $this->attributeModel->getActiveAttributes(),
                'data_types' => ['quantitative', 'qualitative'],
                'stats' => $this->subComponentModel->getSubComponentStats(),
                'applicability_stats' => $this->subComponentModel->getApplicabilityStats()
            ];
            
            $this->render('mbgl-config/sub-components', $data);
            
        } catch (\Exception $e) {
            $this->handleError('Failed to load sub-components', $e);
        }
    }
    
    /**
     * Create new sub-component
     */
    public function createSubComponent(): void
    {
        $this->requireAuth(['Super Admin']);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $validator = new Validator($_POST);
                $validator->required(['attribute_id', 'component_code', 'component_name', 'max_points', 'data_type'])
                         ->integer(['attribute_id', 'sort_order'])
                         ->string(['component_code', 'component_name', 'component_description', 'data_type'])
                         ->float(['max_points'])
                         ->in('data_type', ['quantitative', 'qualitative'])
                         ->boolean(['applicable_to_schools', 'applicable_to_colleges', 'applicable_to_faculties']);
                
                if (!$validator->isValid()) {
                    throw new \Exception('Validation failed: ' . implode(', ', $validator->getErrors()));
                }
                
                $data = $validator->getData();
                
                // Validate unique code
                if (!$this->subComponentModel->validateUniqueCode($data['component_code'])) {
                    throw new \Exception('Component code already exists');
                }
                
                // Set default sort order if not provided
                if (empty($data['sort_order'])) {
                    $data['sort_order'] = $this->subComponentModel->getNextSortOrder($data['attribute_id']);
                }
                
                // Set default applicability
                $data['applicable_to_schools'] = isset($data['applicable_to_schools']) ? 1 : 0;
                $data['applicable_to_colleges'] = isset($data['applicable_to_colleges']) ? 1 : 0;
                $data['applicable_to_faculties'] = isset($data['applicable_to_faculties']) ? 1 : 0;
                $data['status'] = 'active';
                
                $subComponentId = $this->subComponentModel->create($data);
                
                Session::setFlash('success', 'Sub-component created successfully');
                $this->redirect('/mbgl-config/sub-components');
                
            } catch (\Exception $e) {
                Session::setFlash('error', $e->getMessage());
                $this->redirect('/mbgl-config/sub-components');
            }
        }
        
        $data = [
            'page_title' => 'Create Sub-Component',
            'attributes' => $this->attributeModel->getActiveAttributes(),
            'data_types' => ['quantitative', 'qualitative']
        ];
        
        $this->render('mbgl-config/create-sub-component', $data);
    }
    
    /**
     * Display validation rules management
     */
    public function validationRules(): void
    {
        $this->requireAuth(['Super Admin', 'NAAC Coordinator']);
        
        try {
            $page = (int)($_GET['page'] ?? 1);
            $search = $_GET['search'] ?? '';
            $subComponentId = (int)($_GET['sub_component_id'] ?? 0);
            
            if ($search) {
                $validationRules = $this->validationRuleModel->search($search, $subComponentId ?: null);
                $pagination = null;
            } else {
                $validationRules = $this->validationRuleModel->getValidationRulesWithSubComponent($subComponentId ?: null);
                $pagination = null; // Implement pagination if needed
            }
            
            $data = [
                'page_title' => 'Validation Rules Management',
                'validation_rules' => $validationRules,
                'pagination' => $pagination,
                'search' => $search,
                'selected_sub_component_id' => $subComponentId,
                'sub_components' => $this->subComponentModel->getSubComponentsWithValidationRules(),
                'validation_types' => ['regex', 'reference', 'range', 'exact_match', 'not_empty', 'binary'],
                'stats' => $this->validationRuleModel->getValidationStats()
            ];
            
            $this->render('mbgl-config/validation-rules', $data);
            
        } catch (\Exception $e) {
            $this->handleError('Failed to load validation rules', $e);
        }
    }
    
    /**
     * Display SDG indicators management
     */
    public function sdgIndicators(): void
    {
        $this->requireAuth(['Super Admin', 'NAAC Coordinator']);
        
        try {
            $page = (int)($_GET['page'] ?? 1);
            $search = $_GET['search'] ?? '';
            
            if ($search) {
                $indicators = $this->sdgIndicatorModel->search($search);
                $pagination = null;
            } else {
                $result = $this->sdgIndicatorModel->paginate($page, 15, ['status' => 'active'], 'sort_order ASC');
                $indicators = $result['data'];
                $pagination = $result['pagination'];
            }
            
            $data = [
                'page_title' => 'SDG Indicators Management',
                'indicators' => $indicators,
                'pagination' => $pagination,
                'search' => $search,
                'usage_stats' => $this->sdgIndicatorModel->getUsageStats(),
                'popular_indicators' => $this->sdgIndicatorModel->getPopularIndicators(5),
                'statistics' => $this->sdgIndicatorModel->getIndicatorStatistics()
            ];
            
            $this->render('mbgl-config/sdg-indicators', $data);
            
        } catch (\Exception $e) {
            $this->handleError('Failed to load SDG indicators', $e);
        }
    }
    
    /**
     * Display programme codes management
     */
    public function programmeCodes(): void
    {
        $this->requireAuth(['Super Admin', 'NAAC Coordinator']);
        
        try {
            $page = (int)($_GET['page'] ?? 1);
            $search = $_GET['search'] ?? '';
            $instituteId = (int)($_GET['institute_id'] ?? 0);
            
            if ($search) {
                $programmes = $this->programmeCodeModel->search($search, $instituteId ?: null);
                $pagination = null;
            } else {
                $programmes = $this->programmeCodeModel->getProgrammesWithInstitute($instituteId ?: null);
                $pagination = null; // Implement pagination if needed
            }
            
            $data = [
                'page_title' => 'Programme Codes Management',
                'programmes' => $programmes,
                'pagination' => $pagination,
                'search' => $search,
                'selected_institute_id' => $instituteId,
                'institutes' => (new \SIU\MBGL\Models\Institute())->getActiveInstitutes(),
                'programme_types' => $this->programmeCodeModel->getProgrammeTypes(),
                'stats' => $this->programmeCodeModel->getProgrammeStats(),
                'institute_summary' => $this->programmeCodeModel->getInstituteProgrammeSummary()
            ];
            
            $this->render('mbgl-config/programme-codes', $data);
            
        } catch (\Exception $e) {
            $this->handleError('Failed to load programme codes', $e);
        }
    }
    
    /**
     * Export configuration data
     */
    public function exportConfig(): void
    {
        $this->requireAuth(['Super Admin']);
        
        try {
            $type = $_GET['type'] ?? 'all';
            
            $exportData = [];
            
            switch ($type) {
                case 'attributes':
                    $exportData['attributes'] = $this->attributeModel->getActiveAttributes();
                    break;
                    
                case 'sub_components':
                    $exportData['sub_components'] = $this->subComponentModel->getSubComponentsWithValidationRules();
                    break;
                    
                case 'validation_rules':
                    $exportData['validation_rules'] = $this->validationRuleModel->getValidationRulesWithSubComponent();
                    break;
                    
                case 'sdg_indicators':
                    $exportData['sdg_indicators'] = $this->sdgIndicatorModel->exportIndicators();
                    break;
                    
                case 'programme_codes':
                    $exportData['programme_codes'] = $this->programmeCodeModel->exportProgrammes();
                    break;
                    
                default:
                    $exportData = [
                        'attributes' => $this->attributeModel->getActiveAttributes(),
                        'sub_components' => $this->subComponentModel->getSubComponentsWithValidationRules(),
                        'validation_rules' => $this->validationRuleModel->getValidationRulesWithSubComponent(),
                        'sdg_indicators' => $this->sdgIndicatorModel->exportIndicators(),
                        'programme_codes' => $this->programmeCodeModel->exportProgrammes()
                    ];
            }
            
            $filename = 'mbgl_config_' . $type . '_' . date('Y-m-d_H-i-s') . '.json';
            
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            echo json_encode($exportData, JSON_PRETTY_PRINT);
            exit;
            
        } catch (\Exception $e) {
            Session::setFlash('error', 'Export failed: ' . $e->getMessage());
            $this->redirect('/mbgl-config');
        }
    }
}
