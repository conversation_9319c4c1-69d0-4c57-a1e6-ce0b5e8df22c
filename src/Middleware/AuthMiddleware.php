<?php

namespace SIU\MBGL\Middleware;

use SIU\MBGL\Core\Session;

/**
 * Authentication Middleware
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0
 */
class AuthMiddleware
{
    private Session $session;
    
    public function __construct()
    {
        $this->session = Session::getInstance();
    }
    
    public function handle(): bool
    {
        if (!$this->session->isLoggedIn()) {
            // Store the intended URL for redirect after login
            $this->session->set('intended_url', $_SERVER['REQUEST_URI']);
            
            // Redirect to login page
            header('Location: /login');
            exit;
        }
        
        return true;
    }
}
