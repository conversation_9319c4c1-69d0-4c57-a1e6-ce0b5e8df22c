<?php

namespace SIU\MBGL\Models;

use SIU\MBGL\Core\Model;

/**
 * MBGL Attribute Model
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Manages the 10 quality attributes (A1-A10) of the MBGL framework
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
class MBGLAttribute extends Model
{
    protected string $table = 'mbgl_attributes';
    
    protected array $fillable = [
        'attribute_code',
        'attribute_name',
        'attribute_type',
        'max_points',
        'description',
        'sort_order',
        'status'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'max_points' => 'int',
        'sort_order' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Get attribute by code (A1, A2, etc.)
     */
    public function findByCode(string $code): ?array
    {
        return $this->findBy('attribute_code', $code);
    }
    
    /**
     * Get attributes by type (INPUT, PROCESS, OUTCOME, EXTENDED)
     */
    public function findByType(string $type): array
    {
        return $this->findAll(['attribute_type' => $type, 'status' => 'active'], 'sort_order ASC');
    }
    
    /**
     * Get all active attributes ordered by sort_order
     */
    public function getActiveAttributes(): array
    {
        return $this->findAll(['status' => 'active'], 'sort_order ASC');
    }
    
    /**
     * Get attributes with their sub-components
     */
    public function getAttributesWithSubComponents(): array
    {
        $sql = "SELECT 
                    ma.*,
                    COUNT(sc.id) as sub_component_count,
                    SUM(sc.max_points) as total_sub_component_points
                FROM mbgl_attributes ma
                LEFT JOIN sub_components sc ON ma.id = sc.attribute_id AND sc.status = 'active'
                WHERE ma.status = 'active'
                GROUP BY ma.id
                ORDER BY ma.sort_order";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get attribute with its sub-components
     */
    public function getAttributeWithSubComponents(int $attributeId): ?array
    {
        $attribute = $this->find($attributeId);
        if (!$attribute) {
            return null;
        }
        
        $sql = "SELECT * FROM sub_components 
                WHERE attribute_id = :attribute_id AND status = 'active' 
                ORDER BY sort_order ASC";
        
        $subComponents = $this->db->fetchAll($sql, ['attribute_id' => $attributeId]);
        
        $attribute['sub_components'] = $subComponents;
        $attribute['sub_component_count'] = count($subComponents);
        $attribute['total_sub_component_points'] = array_sum(array_column($subComponents, 'max_points'));
        
        return $attribute;
    }
    
    /**
     * Get attribute statistics
     */
    public function getAttributeStats(): array
    {
        $sql = "SELECT 
                    attribute_type,
                    COUNT(*) as count,
                    SUM(max_points) as total_points
                FROM mbgl_attributes 
                WHERE status = 'active'
                GROUP BY attribute_type
                ORDER BY 
                    CASE attribute_type
                        WHEN 'INPUT' THEN 1
                        WHEN 'PROCESS' THEN 2
                        WHEN 'OUTCOME' THEN 3
                        WHEN 'EXTENDED' THEN 4
                    END";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get total maximum points for all attributes
     */
    public function getTotalMaxPoints(): int
    {
        $sql = "SELECT SUM(max_points) as total FROM mbgl_attributes WHERE status = 'active'";
        $result = $this->db->fetch($sql);
        
        return (int)($result['total'] ?? 0);
    }
    
    /**
     * Get attributes for scoring calculation
     */
    public function getAttributesForScoring(int $instituteId, string $academicYear): array
    {
        $sql = "SELECT 
                    ma.*,
                    COALESCE(ats.total_score, 0) as current_score,
                    COALESCE(ats.score_percentage, 0) as score_percentage,
                    ats.last_calculated
                FROM mbgl_attributes ma
                LEFT JOIN attribute_scores ats ON ma.id = ats.attribute_id 
                    AND ats.institute_id = :institute_id 
                    AND ats.academic_year = :academic_year
                WHERE ma.status = 'active'
                ORDER BY ma.sort_order";
        
        return $this->db->fetchAll($sql, [
            'institute_id' => $instituteId,
            'academic_year' => $academicYear
        ]);
    }
    
    /**
     * Validate unique attribute code
     */
    public function validateUniqueCode(string $code, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE attribute_code = :code";
        $params = ['code' => $code];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] == 0;
    }
    
    /**
     * Get next sort order for new attribute
     */
    public function getNextSortOrder(): int
    {
        $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table}";
        $result = $this->db->fetch($sql);
        
        return ((int)($result['max_order'] ?? 0)) + 1;
    }
    
    /**
     * Update sort orders for attributes
     */
    public function updateSortOrders(array $attributeOrders): bool
    {
        $this->beginTransaction();
        
        try {
            foreach ($attributeOrders as $attributeId => $sortOrder) {
                $this->update($attributeId, ['sort_order' => $sortOrder]);
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
    
    /**
     * Search attributes
     */
    public function search(string $query): array
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE (attribute_name LIKE :query 
                   OR attribute_code LIKE :query 
                   OR description LIKE :query)
                AND status = 'active'
                ORDER BY sort_order";
        
        return $this->db->fetchAll($sql, ['query' => "%{$query}%"]);
    }
    
    /**
     * Get attribute type distribution
     */
    public function getTypeDistribution(): array
    {
        $sql = "SELECT 
                    attribute_type,
                    COUNT(*) as count,
                    ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM mbgl_attributes WHERE status = 'active')), 2) as percentage
                FROM mbgl_attributes 
                WHERE status = 'active'
                GROUP BY attribute_type
                ORDER BY count DESC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Clone attribute with sub-components
     */
    public function cloneAttribute(int $sourceId, array $newData): ?int
    {
        $this->beginTransaction();
        
        try {
            $sourceAttribute = $this->find($sourceId);
            if (!$sourceAttribute) {
                throw new \Exception('Source attribute not found');
            }
            
            // Create new attribute
            $attributeData = array_merge($sourceAttribute, $newData);
            unset($attributeData['id'], $attributeData['created_at'], $attributeData['updated_at']);
            
            $newAttributeId = $this->create($attributeData);
            
            // Clone sub-components
            $subComponentModel = new SubComponent();
            $subComponents = $subComponentModel->findAll(['attribute_id' => $sourceId]);
            
            foreach ($subComponents as $subComponent) {
                $subComponentData = $subComponent;
                unset($subComponentData['id'], $subComponentData['created_at'], $subComponentData['updated_at']);
                $subComponentData['attribute_id'] = $newAttributeId;
                
                $subComponentModel->create($subComponentData);
            }
            
            $this->commit();
            return $newAttributeId;
        } catch (\Exception $e) {
            $this->rollback();
            return null;
        }
    }
}
