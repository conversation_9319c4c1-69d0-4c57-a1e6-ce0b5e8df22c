<?php

namespace SIU\MBGL\Models;

use SIU\MBGL\Core\Model;

/**
 * Programme Code Model
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Manages programme codes for institutes
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0
 */
class ProgrammeCode extends Model
{
    protected string $table = 'programme_codes';
    
    protected array $fillable = [
        'institute_id',
        'programme_code',
        'programme_name',
        'programme_type',
        'status'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'institute_id' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Get programme by code
     */
    public function findByCode(string $code): ?array
    {
        return $this->findBy('programme_code', $code);
    }
    
    /**
     * Get programmes by institute ID
     */
    public function findByInstitute(int $instituteId): array
    {
        return $this->findAll(['institute_id' => $instituteId, 'status' => 'active'], 'programme_name ASC');
    }
    
    /**
     * Get programmes by type
     */
    public function findByType(string $type): array
    {
        return $this->findAll(['programme_type' => $type, 'status' => 'active'], 'programme_name ASC');
    }
    
    /**
     * Get programmes with institute details
     */
    public function getProgrammesWithInstitute(int $instituteId = null): array
    {
        $sql = "SELECT 
                    pc.*,
                    i.institute_name,
                    i.institute_short_name,
                    i.institute_code,
                    i.faculty,
                    i.city
                FROM programme_codes pc
                JOIN institutes i ON pc.institute_id = i.id
                WHERE pc.status = 'active' AND i.status = 'active'";
        
        $params = [];
        
        if ($instituteId) {
            $sql .= " AND pc.institute_id = :institute_id";
            $params['institute_id'] = $instituteId;
        }
        
        $sql .= " ORDER BY i.institute_name, pc.programme_name";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get programme statistics
     */
    public function getProgrammeStats(): array
    {
        $sql = "SELECT 
                    programme_type,
                    COUNT(*) as count
                FROM programme_codes 
                WHERE status = 'active'
                GROUP BY programme_type
                ORDER BY count DESC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get programmes by faculty
     */
    public function getProgrammesByFaculty(string $faculty): array
    {
        $sql = "SELECT 
                    pc.*,
                    i.institute_name,
                    i.institute_short_name
                FROM programme_codes pc
                JOIN institutes i ON pc.institute_id = i.id
                WHERE i.faculty = :faculty 
                AND pc.status = 'active' 
                AND i.status = 'active'
                ORDER BY i.institute_name, pc.programme_name";
        
        return $this->db->fetchAll($sql, ['faculty' => $faculty]);
    }
    
    /**
     * Get programmes by city
     */
    public function getProgrammesByCity(string $city): array
    {
        $sql = "SELECT 
                    pc.*,
                    i.institute_name,
                    i.institute_short_name,
                    i.faculty
                FROM programme_codes pc
                JOIN institutes i ON pc.institute_id = i.id
                WHERE i.city = :city 
                AND pc.status = 'active' 
                AND i.status = 'active'
                ORDER BY i.institute_name, pc.programme_name";
        
        return $this->db->fetchAll($sql, ['city' => $city]);
    }
    
    /**
     * Search programmes
     */
    public function search(string $query, int $instituteId = null): array
    {
        $sql = "SELECT 
                    pc.*,
                    i.institute_name,
                    i.institute_short_name
                FROM programme_codes pc
                JOIN institutes i ON pc.institute_id = i.id
                WHERE (pc.programme_name LIKE :query 
                   OR pc.programme_code LIKE :query 
                   OR pc.programme_type LIKE :query
                   OR i.institute_name LIKE :query)
                AND pc.status = 'active' 
                AND i.status = 'active'";
        
        $params = ['query' => "%{$query}%"];
        
        if ($instituteId) {
            $sql .= " AND pc.institute_id = :institute_id";
            $params['institute_id'] = $instituteId;
        }
        
        $sql .= " ORDER BY i.institute_name, pc.programme_name";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Validate unique programme code
     */
    public function validateUniqueCode(string $code, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE programme_code = :code";
        $params = ['code' => $code];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] == 0;
    }
    
    /**
     * Get programme types
     */
    public function getProgrammeTypes(): array
    {
        $sql = "SELECT DISTINCT programme_type 
                FROM {$this->table} 
                WHERE status = 'active' 
                AND programme_type IS NOT NULL 
                AND programme_type != ''
                ORDER BY programme_type";
        
        return array_column($this->db->fetchAll($sql), 'programme_type');
    }
    
    /**
     * Get programmes for dropdown/select options
     */
    public function getProgrammeOptions(int $instituteId = null): array
    {
        $sql = "SELECT 
                    pc.id,
                    pc.programme_code,
                    pc.programme_name,
                    pc.programme_type,
                    i.institute_short_name
                FROM programme_codes pc
                JOIN institutes i ON pc.institute_id = i.id
                WHERE pc.status = 'active' AND i.status = 'active'";
        
        $params = [];
        
        if ($instituteId) {
            $sql .= " AND pc.institute_id = :institute_id";
            $params['institute_id'] = $instituteId;
        }
        
        $sql .= " ORDER BY i.institute_name, pc.programme_name";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get institute programme summary
     */
    public function getInstituteProgrammeSummary(): array
    {
        $sql = "SELECT 
                    i.institute_name,
                    i.institute_short_name,
                    i.faculty,
                    i.city,
                    COUNT(pc.id) as programme_count,
                    GROUP_CONCAT(DISTINCT pc.programme_type ORDER BY pc.programme_type) as programme_types
                FROM institutes i
                LEFT JOIN programme_codes pc ON i.id = pc.institute_id AND pc.status = 'active'
                WHERE i.status = 'active'
                GROUP BY i.id
                ORDER BY i.institute_name";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Bulk import programmes
     */
    public function bulkImport(array $programmes): array
    {
        $this->beginTransaction();
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];
        
        try {
            foreach ($programmes as $index => $programme) {
                try {
                    // Validate required fields
                    if (empty($programme['programme_code'])) {
                        throw new \Exception('Programme code is required');
                    }
                    
                    if (empty($programme['programme_name'])) {
                        throw new \Exception('Programme name is required');
                    }
                    
                    if (empty($programme['institute_id'])) {
                        throw new \Exception('Institute ID is required');
                    }
                    
                    // Check for duplicates
                    if (!$this->validateUniqueCode($programme['programme_code'])) {
                        throw new \Exception('Programme code already exists');
                    }
                    
                    // Validate institute exists
                    $instituteModel = new Institute();
                    if (!$instituteModel->exists($programme['institute_id'])) {
                        throw new \Exception('Institute does not exist');
                    }
                    
                    // Set default values
                    $programme['status'] = $programme['status'] ?? 'active';
                    
                    $this->create($programme);
                    $results['success']++;
                    
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Row " . ($index + 1) . ": " . $e->getMessage();
                }
            }
            
            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            $results['errors'][] = "Transaction failed: " . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Export programmes to array
     */
    public function exportProgrammes(int $instituteId = null): array
    {
        $sql = "SELECT 
                    pc.programme_code,
                    pc.programme_name,
                    pc.programme_type,
                    i.institute_name,
                    i.institute_code,
                    i.faculty,
                    i.city,
                    pc.status,
                    pc.created_at
                FROM programme_codes pc
                JOIN institutes i ON pc.institute_id = i.id";
        
        $params = [];
        
        if ($instituteId) {
            $sql .= " WHERE pc.institute_id = :institute_id";
            $params['institute_id'] = $instituteId;
        }
        
        $sql .= " ORDER BY i.institute_name, pc.programme_name";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get programme usage in submissions
     */
    public function getProgrammeUsage(int $programmeId = null): array
    {
        $sql = "SELECT 
                    pc.programme_code,
                    pc.programme_name,
                    i.institute_name,
                    COUNT(ds.id) as submission_count,
                    COUNT(DISTINCT ds.academic_year) as academic_years
                FROM programme_codes pc
                JOIN institutes i ON pc.institute_id = i.id
                LEFT JOIN data_submissions ds ON FIND_IN_SET(pc.programme_code, ds.programme_codes) > 0
                WHERE pc.status = 'active'";
        
        $params = [];
        
        if ($programmeId) {
            $sql .= " AND pc.id = :programme_id";
            $params['programme_id'] = $programmeId;
        }
        
        $sql .= " GROUP BY pc.id
                  ORDER BY submission_count DESC, pc.programme_name";
        
        return $this->db->fetchAll($sql, $params);
    }
}
