<?php

namespace SIU\MBGL\Models;

use SIU\MBGL\Core\Model;

/**
 * User Model
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
class User extends Model
{
    protected string $table = 'users';
    
    protected array $fillable = [
        'institute_id',
        'name',
        'email',
        'security_code',
        'password_hash',
        'role',
        'status',
        'last_login',
        'login_attempts',
        'locked_until'
    ];
    
    protected array $hidden = [
        'password_hash',
        'security_code'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'institute_id' => 'int',
        'login_attempts' => 'int',
        'last_login' => 'datetime',
        'locked_until' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    public function findByEmail(string $email): ?array
    {
        return $this->findBy('email', $email);
    }
    
    public function findByEmailAndSecurityCode(string $email, string $securityCode): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE email = :email AND security_code = :security_code LIMIT 1";
        $result = $this->db->fetch($sql, [
            'email' => $email,
            'security_code' => $securityCode
        ]);
        
        return $result ? $this->castAttributes($result) : null;
    }
    
    public function authenticate(string $email, string $securityCode): ?array
    {
        $user = $this->findByEmailAndSecurityCode($email, $securityCode);
        
        if (!$user) {
            return null;
        }
        
        // Check if user is locked
        if ($user['status'] === 'locked' && $user['locked_until']) {
            $lockedUntil = new \DateTime($user['locked_until']);
            if ($lockedUntil > new \DateTime()) {
                return null; // Still locked
            } else {
                // Unlock user
                $this->update($user['id'], [
                    'status' => 'active',
                    'locked_until' => null,
                    'login_attempts' => 0
                ]);
                $user['status'] = 'active';
            }
        }
        
        if ($user['status'] !== 'active') {
            return null;
        }
        
        // Update last login
        $this->update($user['id'], [
            'last_login' => date('Y-m-d H:i:s'),
            'login_attempts' => 0
        ]);
        
        return $user;
    }
    
    public function incrementLoginAttempts(string $email): void
    {
        $user = $this->findByEmail($email);
        if (!$user) {
            return;
        }
        
        $attempts = $user['login_attempts'] + 1;
        $updateData = ['login_attempts' => $attempts];
        
        // Lock user after 3 failed attempts
        if ($attempts >= 3) {
            $updateData['status'] = 'locked';
            $updateData['locked_until'] = date('Y-m-d H:i:s', strtotime('+30 minutes'));
        }
        
        $this->update($user['id'], $updateData);
    }
    
    public function getWithInstitute(int $userId): ?array
    {
        $sql = "SELECT u.*, i.institute_name, i.institute_short_name, i.faculty, i.city 
                FROM {$this->table} u 
                LEFT JOIN institutes i ON u.institute_id = i.id 
                WHERE u.id = :id LIMIT 1";
        
        $result = $this->db->fetch($sql, ['id' => $userId]);
        return $result ? $this->castAttributes($result) : null;
    }
    
    public function findByRole(string $role): array
    {
        return $this->findAll(['role' => $role], 'name ASC');
    }
    
    public function findByInstitute(int $instituteId): array
    {
        return $this->findAll(['institute_id' => $instituteId], 'name ASC');
    }
    
    public function getActiveUsers(): array
    {
        return $this->findAll(['status' => 'active'], 'name ASC');
    }
    
    public function getRoleStats(): array
    {
        $sql = "SELECT role, COUNT(*) as count FROM {$this->table} WHERE status = 'active' GROUP BY role ORDER BY role";
        return $this->db->fetchAll($sql);
    }
    
    public function getInstituteUsers(int $instituteId): array
    {
        $sql = "SELECT u.*, i.institute_name 
                FROM {$this->table} u 
                JOIN institutes i ON u.institute_id = i.id 
                WHERE u.institute_id = :institute_id 
                ORDER BY u.name";
        
        return $this->db->fetchAll($sql, ['institute_id' => $instituteId]);
    }
    
    public function search(string $query): array
    {
        $sql = "SELECT u.*, i.institute_name 
                FROM {$this->table} u 
                LEFT JOIN institutes i ON u.institute_id = i.id 
                WHERE (u.name LIKE :query OR u.email LIKE :query)
                AND u.status = 'active'
                ORDER BY u.name";
        
        return $this->db->fetchAll($sql, ['query' => "%{$query}%"]);
    }
    
    public function validateUniqueEmail(string $email, ?int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = :email";
        $params = ['email' => $email];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] == 0;
    }
    
    public function updatePassword(int $userId, string $newPassword): bool
    {
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);
        return $this->update($userId, ['password_hash' => $hashedPassword]);
    }
    
    public function verifyPassword(int $userId, string $password): bool
    {
        $sql = "SELECT password_hash FROM {$this->table} WHERE id = :id LIMIT 1";
        $result = $this->db->fetch($sql, ['id' => $userId]);
        
        if (!$result || !$result['password_hash']) {
            return false;
        }
        
        return password_verify($password, $result['password_hash']);
    }
    
    public function getActivityLogs(int $userId, int $limit = 50): array
    {
        $sql = "SELECT * FROM activity_logs 
                WHERE user_id = :user_id 
                ORDER BY created_at DESC 
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, [
            'user_id' => $userId,
            'limit' => $limit
        ]);
    }
    
    public function getSubmissions(int $userId): array
    {
        $sql = "SELECT ds.*, sc.component_name, i.institute_name 
                FROM data_submissions ds 
                JOIN sub_components sc ON ds.sub_component_id = sc.id 
                JOIN institutes i ON ds.institute_id = i.id 
                WHERE ds.user_id = :user_id 
                ORDER BY ds.created_at DESC";
        
        return $this->db->fetchAll($sql, ['user_id' => $userId]);
    }
    
    public function canAccessInstitute(int $userId, int $instituteId): bool
    {
        $user = $this->find($userId);
        
        if (!$user) {
            return false;
        }
        
        // Super Admin can access all institutes
        if ($user['role'] === 'Super Admin') {
            return true;
        }
        
        // Other users can only access their assigned institute
        return $user['institute_id'] == $instituteId;
    }
    
    public function hasRole(int $userId, array $roles): bool
    {
        $user = $this->find($userId);
        
        if (!$user) {
            return false;
        }
        
        return in_array($user['role'], $roles);
    }
}
