<?php

namespace SIU\MBGL\Models;

use SIU\MBGL\Core\Model;

/**
 * Sub Component Model
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Manages sub-components within MBGL attributes
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
class SubComponent extends Model
{
    protected string $table = 'sub_components';
    
    protected array $fillable = [
        'attribute_id',
        'component_code',
        'component_name',
        'component_description',
        'max_points',
        'data_type',
        'applicable_to_schools',
        'applicable_to_colleges',
        'applicable_to_faculties',
        'sort_order',
        'status'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'attribute_id' => 'int',
        'max_points' => 'float',
        'applicable_to_schools' => 'bool',
        'applicable_to_colleges' => 'bool',
        'applicable_to_faculties' => 'bool',
        'sort_order' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Get sub-component by code
     */
    public function findByCode(string $code): ?array
    {
        return $this->findBy('component_code', $code);
    }
    
    /**
     * Get sub-components by attribute ID
     */
    public function findByAttribute(int $attributeId): array
    {
        return $this->findAll(['attribute_id' => $attributeId, 'status' => 'active'], 'sort_order ASC');
    }
    
    /**
     * Get sub-components by data type
     */
    public function findByDataType(string $dataType): array
    {
        return $this->findAll(['data_type' => $dataType, 'status' => 'active'], 'component_name ASC');
    }
    
    /**
     * Get sub-component with attribute details
     */
    public function getSubComponentWithAttribute(int $subComponentId): ?array
    {
        $sql = "SELECT 
                    sc.*,
                    ma.attribute_name,
                    ma.attribute_code,
                    ma.attribute_type
                FROM sub_components sc
                JOIN mbgl_attributes ma ON sc.attribute_id = ma.id
                WHERE sc.id = :id AND sc.status = 'active'";
        
        return $this->db->fetch($sql, ['id' => $subComponentId]);
    }
    
    /**
     * Get sub-components with validation rules
     */
    public function getSubComponentsWithValidationRules(int $attributeId = null): array
    {
        $sql = "SELECT 
                    sc.*,
                    ma.attribute_name,
                    ma.attribute_code,
                    COUNT(vr.id) as validation_rule_count
                FROM sub_components sc
                JOIN mbgl_attributes ma ON sc.attribute_id = ma.id
                LEFT JOIN validation_rules vr ON sc.id = vr.sub_component_id AND vr.status = 'active'
                WHERE sc.status = 'active'";
        
        $params = [];
        
        if ($attributeId) {
            $sql .= " AND sc.attribute_id = :attribute_id";
            $params['attribute_id'] = $attributeId;
        }
        
        $sql .= " GROUP BY sc.id ORDER BY ma.sort_order, sc.sort_order";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get sub-components applicable to specific institute type
     */
    public function getApplicableSubComponents(string $instituteType, int $attributeId = null): array
    {
        $applicableColumn = match($instituteType) {
            'school' => 'applicable_to_schools',
            'college' => 'applicable_to_colleges',
            'faculty' => 'applicable_to_faculties',
            default => 'applicable_to_colleges'
        };
        
        $sql = "SELECT 
                    sc.*,
                    ma.attribute_name,
                    ma.attribute_code
                FROM sub_components sc
                JOIN mbgl_attributes ma ON sc.attribute_id = ma.id
                WHERE sc.status = 'active' 
                AND sc.{$applicableColumn} = 1";
        
        $params = [];
        
        if ($attributeId) {
            $sql .= " AND sc.attribute_id = :attribute_id";
            $params['attribute_id'] = $attributeId;
        }
        
        $sql .= " ORDER BY ma.sort_order, sc.sort_order";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get sub-component statistics
     */
    public function getSubComponentStats(): array
    {
        $sql = "SELECT 
                    data_type,
                    COUNT(*) as count,
                    AVG(max_points) as avg_points,
                    SUM(max_points) as total_points
                FROM sub_components 
                WHERE status = 'active'
                GROUP BY data_type
                ORDER BY count DESC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get sub-components for scoring
     */
    public function getSubComponentsForScoring(int $instituteId, string $academicYear, int $attributeId = null): array
    {
        $sql = "SELECT 
                    sc.*,
                    ma.attribute_name,
                    ma.attribute_code,
                    COALESCE(s.calculated_score, 0) as current_score,
                    COALESCE(s.score_percentage, 0) as score_percentage,
                    s.last_calculated,
                    ds.submission_status,
                    ds.validation_status
                FROM sub_components sc
                JOIN mbgl_attributes ma ON sc.attribute_id = ma.id
                LEFT JOIN scores s ON sc.id = s.sub_component_id 
                    AND s.institute_id = :institute_id 
                    AND s.academic_year = :academic_year
                LEFT JOIN data_submissions ds ON sc.id = ds.sub_component_id 
                    AND ds.institute_id = :institute_id 
                    AND ds.academic_year = :academic_year
                WHERE sc.status = 'active'";
        
        $params = [
            'institute_id' => $instituteId,
            'academic_year' => $academicYear
        ];
        
        if ($attributeId) {
            $sql .= " AND sc.attribute_id = :attribute_id";
            $params['attribute_id'] = $attributeId;
        }
        
        $sql .= " ORDER BY ma.sort_order, sc.sort_order";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Validate unique component code
     */
    public function validateUniqueCode(string $code, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE component_code = :code";
        $params = ['code' => $code];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] == 0;
    }
    
    /**
     * Get next sort order for attribute
     */
    public function getNextSortOrder(int $attributeId): int
    {
        $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table} WHERE attribute_id = :attribute_id";
        $result = $this->db->fetch($sql, ['attribute_id' => $attributeId]);
        
        return ((int)($result['max_order'] ?? 0)) + 1;
    }
    
    /**
     * Update sort orders for sub-components
     */
    public function updateSortOrders(array $subComponentOrders): bool
    {
        $this->beginTransaction();
        
        try {
            foreach ($subComponentOrders as $subComponentId => $sortOrder) {
                $this->update($subComponentId, ['sort_order' => $sortOrder]);
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
    
    /**
     * Search sub-components
     */
    public function search(string $query, int $attributeId = null): array
    {
        $sql = "SELECT 
                    sc.*,
                    ma.attribute_name,
                    ma.attribute_code
                FROM sub_components sc
                JOIN mbgl_attributes ma ON sc.attribute_id = ma.id
                WHERE (sc.component_name LIKE :query 
                   OR sc.component_code LIKE :query 
                   OR sc.component_description LIKE :query)
                AND sc.status = 'active'";
        
        $params = ['query' => "%{$query}%"];
        
        if ($attributeId) {
            $sql .= " AND sc.attribute_id = :attribute_id";
            $params['attribute_id'] = $attributeId;
        }
        
        $sql .= " ORDER BY ma.sort_order, sc.sort_order";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get data type distribution
     */
    public function getDataTypeDistribution(): array
    {
        $sql = "SELECT 
                    data_type,
                    COUNT(*) as count,
                    ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sub_components WHERE status = 'active')), 2) as percentage
                FROM sub_components 
                WHERE status = 'active'
                GROUP BY data_type
                ORDER BY count DESC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get applicability statistics
     */
    public function getApplicabilityStats(): array
    {
        $sql = "SELECT 
                    SUM(applicable_to_schools) as schools_count,
                    SUM(applicable_to_colleges) as colleges_count,
                    SUM(applicable_to_faculties) as faculties_count,
                    COUNT(*) as total_count
                FROM sub_components 
                WHERE status = 'active'";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * Bulk update applicability
     */
    public function bulkUpdateApplicability(array $subComponentIds, array $applicability): bool
    {
        $this->beginTransaction();
        
        try {
            foreach ($subComponentIds as $subComponentId) {
                $this->update($subComponentId, $applicability);
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
}
