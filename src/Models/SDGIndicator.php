<?php

namespace SIU\MBGL\Models;

use SIU\MBGL\Core\Model;

/**
 * SDG Indicator Model
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Manages Sustainable Development Goals (SDG) indicators
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
class SDGIndicator extends Model
{
    protected string $table = 'sdg_indicators';
    
    protected array $fillable = [
        'indicator_name',
        'indicator_description',
        'sort_order',
        'status'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'sort_order' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Get indicator by name
     */
    public function findByName(string $name): ?array
    {
        return $this->findBy('indicator_name', $name);
    }
    
    /**
     * Get all active indicators ordered by sort_order
     */
    public function getActiveIndicators(): array
    {
        return $this->findAll(['status' => 'active'], 'sort_order ASC');
    }
    
    /**
     * Get indicators for dropdown/select options
     */
    public function getIndicatorOptions(): array
    {
        $sql = "SELECT id, indicator_name, indicator_description 
                FROM {$this->table} 
                WHERE status = 'active' 
                ORDER BY sort_order ASC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get indicator usage statistics
     */
    public function getUsageStats(): array
    {
        $sql = "SELECT 
                    si.indicator_name,
                    si.indicator_description,
                    COUNT(DISTINCT ds.institute_id) as institute_count,
                    COUNT(ds.id) as submission_count
                FROM sdg_indicators si
                LEFT JOIN data_submissions ds ON FIND_IN_SET(si.indicator_name, ds.sdg_indicators) > 0
                WHERE si.status = 'active'
                GROUP BY si.id
                ORDER BY submission_count DESC, si.sort_order";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get indicators used by specific institute
     */
    public function getIndicatorsByInstitute(int $instituteId, string $academicYear = null): array
    {
        $sql = "SELECT DISTINCT
                    si.*,
                    COUNT(ds.id) as usage_count
                FROM sdg_indicators si
                JOIN data_submissions ds ON FIND_IN_SET(si.indicator_name, ds.sdg_indicators) > 0
                WHERE ds.institute_id = :institute_id
                AND si.status = 'active'";
        
        $params = ['institute_id' => $instituteId];
        
        if ($academicYear) {
            $sql .= " AND ds.academic_year = :academic_year";
            $params['academic_year'] = $academicYear;
        }
        
        $sql .= " GROUP BY si.id ORDER BY usage_count DESC, si.sort_order";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get most popular indicators
     */
    public function getPopularIndicators(int $limit = 10): array
    {
        $sql = "SELECT 
                    si.*,
                    COUNT(ds.id) as usage_count,
                    COUNT(DISTINCT ds.institute_id) as institute_count
                FROM sdg_indicators si
                LEFT JOIN data_submissions ds ON FIND_IN_SET(si.indicator_name, ds.sdg_indicators) > 0
                WHERE si.status = 'active'
                GROUP BY si.id
                ORDER BY usage_count DESC, institute_count DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    /**
     * Search indicators
     */
    public function search(string $query): array
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE (indicator_name LIKE :query 
                   OR indicator_description LIKE :query)
                AND status = 'active'
                ORDER BY 
                    CASE 
                        WHEN indicator_name LIKE :exact_query THEN 1
                        WHEN indicator_name LIKE :start_query THEN 2
                        ELSE 3
                    END,
                    sort_order";
        
        return $this->db->fetchAll($sql, [
            'query' => "%{$query}%",
            'exact_query' => $query,
            'start_query' => "{$query}%"
        ]);
    }
    
    /**
     * Validate unique indicator name
     */
    public function validateUniqueName(string $name, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE indicator_name = :name";
        $params = ['name' => $name];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] == 0;
    }
    
    /**
     * Get next sort order
     */
    public function getNextSortOrder(): int
    {
        $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table}";
        $result = $this->db->fetch($sql);
        
        return ((int)($result['max_order'] ?? 0)) + 1;
    }
    
    /**
     * Update sort orders
     */
    public function updateSortOrders(array $indicatorOrders): bool
    {
        $this->beginTransaction();
        
        try {
            foreach ($indicatorOrders as $indicatorId => $sortOrder) {
                $this->update($indicatorId, ['sort_order' => $sortOrder]);
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
    
    /**
     * Get indicator trends over academic years
     */
    public function getIndicatorTrends(int $indicatorId = null): array
    {
        $sql = "SELECT 
                    ds.academic_year,
                    si.indicator_name,
                    COUNT(ds.id) as usage_count,
                    COUNT(DISTINCT ds.institute_id) as institute_count
                FROM data_submissions ds
                JOIN sdg_indicators si ON FIND_IN_SET(si.indicator_name, ds.sdg_indicators) > 0
                WHERE si.status = 'active'";
        
        $params = [];
        
        if ($indicatorId) {
            $sql .= " AND si.id = :indicator_id";
            $params['indicator_id'] = $indicatorId;
        }
        
        $sql .= " GROUP BY ds.academic_year, si.id
                  ORDER BY ds.academic_year DESC, usage_count DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get indicators by category/theme
     */
    public function getIndicatorsByTheme(): array
    {
        // Group indicators by common themes based on their names
        $sql = "SELECT 
                    indicator_name,
                    indicator_description,
                    CASE 
                        WHEN indicator_name LIKE '%Health%' OR indicator_name LIKE '%Medical%' THEN 'Health & Well-being'
                        WHEN indicator_name LIKE '%Education%' OR indicator_name LIKE '%Quality%' THEN 'Quality Education'
                        WHEN indicator_name LIKE '%Gender%' OR indicator_name LIKE '%Equality%' THEN 'Gender Equality'
                        WHEN indicator_name LIKE '%Environment%' OR indicator_name LIKE '%Sustainability%' THEN 'Environmental Sustainability'
                        WHEN indicator_name LIKE '%Economic%' OR indicator_name LIKE '%Employment%' THEN 'Economic Growth'
                        WHEN indicator_name LIKE '%Technology%' OR indicator_name LIKE '%Innovation%' THEN 'Innovation & Technology'
                        WHEN indicator_name LIKE '%Infrastructure%' THEN 'Infrastructure'
                        WHEN indicator_name LIKE '%Poverty%' OR indicator_name LIKE '%Hunger%' THEN 'No Poverty & Zero Hunger'
                        ELSE 'Other'
                    END as theme
                FROM {$this->table}
                WHERE status = 'active'
                ORDER BY theme, sort_order";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Bulk import indicators
     */
    public function bulkImport(array $indicators): array
    {
        $this->beginTransaction();
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];
        
        try {
            foreach ($indicators as $index => $indicator) {
                try {
                    // Validate required fields
                    if (empty($indicator['indicator_name'])) {
                        throw new \Exception('Indicator name is required');
                    }
                    
                    // Check for duplicates
                    if (!$this->validateUniqueName($indicator['indicator_name'])) {
                        throw new \Exception('Indicator name already exists');
                    }
                    
                    // Set default values
                    $indicator['sort_order'] = $indicator['sort_order'] ?? $this->getNextSortOrder();
                    $indicator['status'] = $indicator['status'] ?? 'active';
                    
                    $this->create($indicator);
                    $results['success']++;
                    
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Row " . ($index + 1) . ": " . $e->getMessage();
                }
            }
            
            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            $results['errors'][] = "Transaction failed: " . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Export indicators to array
     */
    public function exportIndicators(): array
    {
        $sql = "SELECT 
                    indicator_name,
                    indicator_description,
                    sort_order,
                    status,
                    created_at
                FROM {$this->table}
                ORDER BY sort_order";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get indicator statistics
     */
    public function getIndicatorStatistics(): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_indicators,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_indicators,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_indicators
                FROM {$this->table}";
        
        return $this->db->fetch($sql);
    }
}
