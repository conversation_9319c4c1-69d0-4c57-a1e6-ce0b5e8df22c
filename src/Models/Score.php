<?php

namespace SIU\MBGL\Models;

use SIU\MBGL\Core\Model;

/**
 * Score Model
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Manages scoring calculations for sub-components
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0
 */
class Score extends Model
{
    protected string $table = 'scores';
    
    protected array $fillable = [
        'institute_id',
        'sub_component_id',
        'academic_year',
        'calculated_score',
        'max_possible_score',
        'score_percentage',
        'calculation_method',
        'calculation_details',
        'calculated_by'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'institute_id' => 'int',
        'sub_component_id' => 'int',
        'calculated_score' => 'float',
        'max_possible_score' => 'float',
        'score_percentage' => 'float',
        'calculated_by' => 'int',
        'last_calculated' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Get score by institute, sub-component and academic year
     */
    public function findScore(int $instituteId, int $subComponentId, string $academicYear): ?array
    {
        return $this->findBy('institute_id', $instituteId, [
            'sub_component_id' => $subComponentId,
            'academic_year' => $academicYear
        ]);
    }
    
    /**
     * Get scores by institute and academic year
     */
    public function getInstituteScores(int $instituteId, string $academicYear): array
    {
        $sql = "SELECT 
                    s.*,
                    sc.component_name,
                    sc.component_code,
                    sc.max_points as component_max_points,
                    ma.attribute_name,
                    ma.attribute_code,
                    ma.attribute_type,
                    u.name as calculated_by_name
                FROM scores s
                JOIN sub_components sc ON s.sub_component_id = sc.id
                JOIN mbgl_attributes ma ON sc.attribute_id = ma.id
                LEFT JOIN users u ON s.calculated_by = u.id
                WHERE s.institute_id = :institute_id 
                AND s.academic_year = :academic_year
                ORDER BY ma.sort_order, sc.sort_order";
        
        return $this->db->fetchAll($sql, [
            'institute_id' => $instituteId,
            'academic_year' => $academicYear
        ]);
    }
    
    /**
     * Get scores by attribute
     */
    public function getAttributeScores(int $instituteId, int $attributeId, string $academicYear): array
    {
        $sql = "SELECT 
                    s.*,
                    sc.component_name,
                    sc.component_code,
                    sc.max_points as component_max_points
                FROM scores s
                JOIN sub_components sc ON s.sub_component_id = sc.id
                WHERE s.institute_id = :institute_id 
                AND sc.attribute_id = :attribute_id
                AND s.academic_year = :academic_year
                ORDER BY sc.sort_order";
        
        return $this->db->fetchAll($sql, [
            'institute_id' => $instituteId,
            'attribute_id' => $attributeId,
            'academic_year' => $academicYear
        ]);
    }
    
    /**
     * Calculate and save score for sub-component
     */
    public function calculateScore(int $instituteId, int $subComponentId, string $academicYear, int $calculatedBy): ?array
    {
        $this->beginTransaction();
        
        try {
            // Get sub-component details
            $subComponentModel = new SubComponent();
            $subComponent = $subComponentModel->getSubComponentWithAttribute($subComponentId);
            
            if (!$subComponent) {
                throw new \Exception('Sub-component not found');
            }
            
            // Get submission data for calculation
            $submissionModel = new \SIU\MBGL\Models\DataSubmission();
            $submissions = $submissionModel->getValidSubmissions($instituteId, $subComponentId, $academicYear);
            
            // Calculate score based on data type and submission data
            $calculationResult = $this->performCalculation($subComponent, $submissions);
            
            // Save or update score
            $scoreData = [
                'institute_id' => $instituteId,
                'sub_component_id' => $subComponentId,
                'academic_year' => $academicYear,
                'calculated_score' => $calculationResult['score'],
                'max_possible_score' => $subComponent['max_points'],
                'score_percentage' => $calculationResult['percentage'],
                'calculation_method' => $calculationResult['method'],
                'calculation_details' => json_encode($calculationResult['details']),
                'calculated_by' => $calculatedBy
            ];
            
            // Check if score already exists
            $existingScore = $this->findScore($instituteId, $subComponentId, $academicYear);
            
            if ($existingScore) {
                $this->update($existingScore['id'], $scoreData);
                $scoreId = $existingScore['id'];
            } else {
                $scoreId = $this->create($scoreData);
            }
            
            $this->commit();
            
            // Return the calculated score
            return $this->find($scoreId);
            
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * Perform calculation based on sub-component type and data
     */
    private function performCalculation(array $subComponent, array $submissions): array
    {
        $maxPoints = (float)$subComponent['max_points'];
        $dataType = $subComponent['data_type'];
        
        if (empty($submissions)) {
            return [
                'score' => 0,
                'percentage' => 0,
                'method' => 'no_data',
                'details' => ['message' => 'No valid submissions found']
            ];
        }
        
        switch ($dataType) {
            case 'quantitative':
                return $this->calculateQuantitativeScore($submissions, $maxPoints);
                
            case 'qualitative':
                return $this->calculateQualitativeScore($submissions, $maxPoints);
                
            default:
                return [
                    'score' => 0,
                    'percentage' => 0,
                    'method' => 'unknown_type',
                    'details' => ['message' => 'Unknown data type: ' . $dataType]
                ];
        }
    }
    
    /**
     * Calculate quantitative score
     */
    private function calculateQuantitativeScore(array $submissions, float $maxPoints): array
    {
        $totalRecords = 0;
        $validRecords = 0;
        
        foreach ($submissions as $submission) {
            $totalRecords += $submission['total_records'];
            $validRecords += $submission['valid_records'];
        }
        
        if ($totalRecords == 0) {
            $score = 0;
            $percentage = 0;
        } else {
            // Calculate score based on data quality and completeness
            $dataQuality = $validRecords / $totalRecords;
            $score = $maxPoints * $dataQuality;
            $percentage = ($score / $maxPoints) * 100;
        }
        
        return [
            'score' => round($score, 2),
            'percentage' => round($percentage, 2),
            'method' => 'quantitative_data_quality',
            'details' => [
                'total_records' => $totalRecords,
                'valid_records' => $validRecords,
                'data_quality' => round($dataQuality * 100, 2) . '%',
                'submissions_count' => count($submissions)
            ]
        ];
    }
    
    /**
     * Calculate qualitative score
     */
    private function calculateQualitativeScore(array $submissions, float $maxPoints): array
    {
        $submissionCount = count($submissions);
        $approvedSubmissions = 0;
        
        foreach ($submissions as $submission) {
            if ($submission['submission_status'] === 'approved') {
                $approvedSubmissions++;
            }
        }
        
        if ($submissionCount == 0) {
            $score = 0;
            $percentage = 0;
        } else {
            // Calculate score based on submission approval rate
            $approvalRate = $approvedSubmissions / $submissionCount;
            $score = $maxPoints * $approvalRate;
            $percentage = ($score / $maxPoints) * 100;
        }
        
        return [
            'score' => round($score, 2),
            'percentage' => round($percentage, 2),
            'method' => 'qualitative_approval_rate',
            'details' => [
                'total_submissions' => $submissionCount,
                'approved_submissions' => $approvedSubmissions,
                'approval_rate' => round($approvalRate * 100, 2) . '%'
            ]
        ];
    }
    
    /**
     * Bulk calculate scores for institute
     */
    public function bulkCalculateScores(int $instituteId, string $academicYear, int $calculatedBy, array $subComponentIds = null): array
    {
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];
        
        // Get sub-components to calculate
        $subComponentModel = new SubComponent();
        if ($subComponentIds) {
            $subComponents = [];
            foreach ($subComponentIds as $id) {
                $sc = $subComponentModel->find($id);
                if ($sc) $subComponents[] = $sc;
            }
        } else {
            $subComponents = $subComponentModel->findAll(['status' => 'active']);
        }
        
        foreach ($subComponents as $subComponent) {
            try {
                $this->calculateScore($instituteId, $subComponent['id'], $academicYear, $calculatedBy);
                $results['success']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Sub-component {$subComponent['component_code']}: " . $e->getMessage();
            }
        }
        
        return $results;
    }
    
    /**
     * Get score statistics
     */
    public function getScoreStatistics(int $instituteId = null, string $academicYear = null): array
    {
        $sql = "SELECT 
                    COUNT(*) as total_scores,
                    AVG(score_percentage) as avg_percentage,
                    MIN(score_percentage) as min_percentage,
                    MAX(score_percentage) as max_percentage,
                    SUM(calculated_score) as total_score,
                    SUM(max_possible_score) as total_max_score
                FROM scores s
                WHERE 1=1";
        
        $params = [];
        
        if ($instituteId) {
            $sql .= " AND s.institute_id = :institute_id";
            $params['institute_id'] = $instituteId;
        }
        
        if ($academicYear) {
            $sql .= " AND s.academic_year = :academic_year";
            $params['academic_year'] = $academicYear;
        }
        
        return $this->db->fetch($sql, $params);
    }
    
    /**
     * Get score trends over academic years
     */
    public function getScoreTrends(int $instituteId, int $subComponentId = null): array
    {
        $sql = "SELECT 
                    s.academic_year,
                    AVG(s.score_percentage) as avg_percentage,
                    SUM(s.calculated_score) as total_score,
                    COUNT(*) as score_count
                FROM scores s
                WHERE s.institute_id = :institute_id";
        
        $params = ['institute_id' => $instituteId];
        
        if ($subComponentId) {
            $sql .= " AND s.sub_component_id = :sub_component_id";
            $params['sub_component_id'] = $subComponentId;
        }
        
        $sql .= " GROUP BY s.academic_year
                  ORDER BY s.academic_year DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Delete scores for recalculation
     */
    public function deleteScores(int $instituteId, string $academicYear, array $subComponentIds = null): bool
    {
        $sql = "DELETE FROM {$this->table} 
                WHERE institute_id = :institute_id 
                AND academic_year = :academic_year";
        
        $params = [
            'institute_id' => $instituteId,
            'academic_year' => $academicYear
        ];
        
        if ($subComponentIds) {
            $placeholders = str_repeat('?,', count($subComponentIds) - 1) . '?';
            $sql .= " AND sub_component_id IN ({$placeholders})";
            $params = array_merge($params, $subComponentIds);
        }
        
        return $this->db->execute($sql, $params) > 0;
    }
}
