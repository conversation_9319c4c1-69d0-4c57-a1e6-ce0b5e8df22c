<?php

namespace SIU\MBGL\Models;

use SIU\MBGL\Core\Model;

/**
 * Validation Rule Model
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Manages CSV validation rules for sub-components
 * 
 * <AUTHOR> <PERSON><PERSON>dra <PERSON>y
 * @version 1.0
 */
class ValidationRule extends Model
{
    protected string $table = 'validation_rules';
    
    protected array $fillable = [
        'sub_component_id',
        'column_name',
        'display_name',
        'data_type',
        'is_required',
        'validation_type',
        'validation_params',
        'reference_table',
        'error_message',
        'sort_order',
        'status'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'sub_component_id' => 'int',
        'is_required' => 'bool',
        'sort_order' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Get validation rules by sub-component ID
     */
    public function findBySubComponent(int $subComponentId): array
    {
        return $this->findAll(['sub_component_id' => $subComponentId, 'status' => 'active'], 'sort_order ASC');
    }
    
    /**
     * Get validation rules by validation type
     */
    public function findByValidationType(string $validationType): array
    {
        return $this->findAll(['validation_type' => $validationType, 'status' => 'active'], 'column_name ASC');
    }
    
    /**
     * Get validation rules with sub-component details
     */
    public function getValidationRulesWithSubComponent(int $subComponentId = null): array
    {
        $sql = "SELECT 
                    vr.*,
                    sc.component_name,
                    sc.component_code,
                    ma.attribute_name,
                    ma.attribute_code
                FROM validation_rules vr
                JOIN sub_components sc ON vr.sub_component_id = sc.id
                JOIN mbgl_attributes ma ON sc.attribute_id = ma.id
                WHERE vr.status = 'active'";
        
        $params = [];
        
        if ($subComponentId) {
            $sql .= " AND vr.sub_component_id = :sub_component_id";
            $params['sub_component_id'] = $subComponentId;
        }
        
        $sql .= " ORDER BY ma.sort_order, sc.sort_order, vr.sort_order";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get required validation rules for sub-component
     */
    public function getRequiredRules(int $subComponentId): array
    {
        return $this->findAll([
            'sub_component_id' => $subComponentId,
            'is_required' => 1,
            'status' => 'active'
        ], 'sort_order ASC');
    }
    
    /**
     * Get validation rules by reference table
     */
    public function findByReferenceTable(string $referenceTable): array
    {
        return $this->findAll([
            'reference_table' => $referenceTable,
            'status' => 'active'
        ], 'column_name ASC');
    }
    
    /**
     * Validate data against rules
     */
    public function validateData(int $subComponentId, array $data): array
    {
        $rules = $this->findBySubComponent($subComponentId);
        $errors = [];
        
        foreach ($rules as $rule) {
            $columnName = $rule['column_name'];
            $value = $data[$columnName] ?? null;
            
            // Check required fields
            if ($rule['is_required'] && (empty($value) && $value !== '0')) {
                $errors[$columnName][] = $rule['error_message'] ?: "Field {$rule['display_name']} is required";
                continue;
            }
            
            // Skip validation if value is empty and not required
            if (empty($value) && $value !== '0' && !$rule['is_required']) {
                continue;
            }
            
            // Validate based on validation type
            $isValid = $this->validateValue($value, $rule);
            
            if (!$isValid) {
                $errors[$columnName][] = $rule['error_message'] ?: "Invalid value for {$rule['display_name']}";
            }
        }
        
        return $errors;
    }
    
    /**
     * Validate single value against rule
     */
    private function validateValue($value, array $rule): bool
    {
        switch ($rule['validation_type']) {
            case 'regex':
                return preg_match('/' . $rule['validation_params'] . '/', $value);
                
            case 'reference':
                return $this->validateReference($value, $rule['reference_table']);
                
            case 'range':
                return $this->validateRange($value, $rule['validation_params']);
                
            case 'exact_match':
                return $value === $rule['validation_params'];
                
            case 'not_empty':
                return !empty($value) || $value === '0';
                
            case 'binary':
                return in_array($value, ['0', '1', 0, 1, true, false]);
                
            default:
                return true;
        }
    }
    
    /**
     * Validate reference table value
     */
    private function validateReference($value, string $referenceTable): bool
    {
        if (!$referenceTable) {
            return true;
        }
        
        // Handle different reference tables
        switch ($referenceTable) {
            case 'institute_list':
                $sql = "SELECT COUNT(*) as count FROM institutes WHERE institute_code = :value AND status = 'active'";
                break;
                
            case 'programme_codes':
                $sql = "SELECT COUNT(*) as count FROM programme_codes WHERE programme_code = :value AND status = 'active'";
                break;
                
            case 'sdg_indicators':
                $sql = "SELECT COUNT(*) as count FROM sdg_indicators WHERE indicator_name = :value AND status = 'active'";
                break;
                
            default:
                return true;
        }
        
        $result = $this->db->fetch($sql, ['value' => $value]);
        return $result['count'] > 0;
    }
    
    /**
     * Validate range value
     */
    private function validateRange($value, string $rangeParams): bool
    {
        if (!$rangeParams) {
            return true;
        }
        
        // Parse range parameters (e.g., "1-100" or "min:1,max:100")
        if (strpos($rangeParams, '-') !== false) {
            [$min, $max] = explode('-', $rangeParams);
            return $value >= (float)$min && $value <= (float)$max;
        }
        
        if (strpos($rangeParams, ',') !== false) {
            $params = [];
            foreach (explode(',', $rangeParams) as $param) {
                [$key, $val] = explode(':', $param);
                $params[trim($key)] = (float)trim($val);
            }
            
            $min = $params['min'] ?? null;
            $max = $params['max'] ?? null;
            
            if ($min !== null && $value < $min) return false;
            if ($max !== null && $value > $max) return false;
        }
        
        return true;
    }
    
    /**
     * Get validation rule statistics
     */
    public function getValidationStats(): array
    {
        $sql = "SELECT 
                    validation_type,
                    COUNT(*) as count,
                    SUM(is_required) as required_count
                FROM validation_rules 
                WHERE status = 'active'
                GROUP BY validation_type
                ORDER BY count DESC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get next sort order for sub-component
     */
    public function getNextSortOrder(int $subComponentId): int
    {
        $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table} WHERE sub_component_id = :sub_component_id";
        $result = $this->db->fetch($sql, ['sub_component_id' => $subComponentId]);
        
        return ((int)($result['max_order'] ?? 0)) + 1;
    }
    
    /**
     * Update sort orders for validation rules
     */
    public function updateSortOrders(array $ruleOrders): bool
    {
        $this->beginTransaction();
        
        try {
            foreach ($ruleOrders as $ruleId => $sortOrder) {
                $this->update($ruleId, ['sort_order' => $sortOrder]);
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
    
    /**
     * Search validation rules
     */
    public function search(string $query, int $subComponentId = null): array
    {
        $sql = "SELECT 
                    vr.*,
                    sc.component_name,
                    sc.component_code
                FROM validation_rules vr
                JOIN sub_components sc ON vr.sub_component_id = sc.id
                WHERE (vr.column_name LIKE :query 
                   OR vr.display_name LIKE :query 
                   OR vr.validation_type LIKE :query)
                AND vr.status = 'active'";
        
        $params = ['query' => "%{$query}%"];
        
        if ($subComponentId) {
            $sql .= " AND vr.sub_component_id = :sub_component_id";
            $params['sub_component_id'] = $subComponentId;
        }
        
        $sql .= " ORDER BY vr.sort_order";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Bulk create validation rules
     */
    public function bulkCreate(array $rules): bool
    {
        $this->beginTransaction();
        
        try {
            foreach ($rules as $rule) {
                $this->create($rule);
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
    
    /**
     * Clone validation rules from one sub-component to another
     */
    public function cloneRules(int $sourceSubComponentId, int $targetSubComponentId): bool
    {
        $this->beginTransaction();
        
        try {
            $sourceRules = $this->findBySubComponent($sourceSubComponentId);
            
            foreach ($sourceRules as $rule) {
                $ruleData = $rule;
                unset($ruleData['id'], $ruleData['created_at'], $ruleData['updated_at']);
                $ruleData['sub_component_id'] = $targetSubComponentId;
                
                $this->create($ruleData);
            }
            
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->rollback();
            return false;
        }
    }
}
