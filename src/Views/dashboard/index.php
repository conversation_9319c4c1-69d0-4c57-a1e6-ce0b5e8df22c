<?php
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="py-3">
                <h6 class="text-muted px-3 mb-3">NAVIGATION</h6>
                <nav class="nav flex-column">
                    <a class="nav-link active" href="/dashboard">
                        <i class="bi bi-speedometer2 me-2"></i>
                        Dashboard
                    </a>
                    
                    <?php if ($user['role'] === 'Super Admin'): ?>
                        <a class="nav-link" href="/admin/institutes">
                            <i class="bi bi-building me-2"></i>
                            Institutes
                        </a>
                        <a class="nav-link" href="/admin/users">
                            <i class="bi bi-people me-2"></i>
                            Users
                        </a>
                        <a class="nav-link" href="/admin/mbgl">
                            <i class="bi bi-gear me-2"></i>
                            MBGL Config
                        </a>
                        <a class="nav-link" href="/admin/reports">
                            <i class="bi bi-graph-up me-2"></i>
                            System Reports
                        </a>
                    <?php else: ?>
                        <a class="nav-link" href="/data/upload">
                            <i class="bi bi-cloud-upload me-2"></i>
                            Upload Data
                        </a>
                        <a class="nav-link" href="/data/submissions">
                            <i class="bi bi-list-ul me-2"></i>
                            Submissions
                        </a>
                        <a class="nav-link" href="/scoring">
                            <i class="bi bi-calculator me-2"></i>
                            Scoring
                        </a>
                        <a class="nav-link" href="/reports">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            Reports
                        </a>
                    <?php endif; ?>
                    
                    <hr class="my-3">
                    <a class="nav-link" href="/profile">
                        <i class="bi bi-person me-2"></i>
                        Profile
                    </a>
                    <a class="nav-link" href="/help">
                        <i class="bi bi-question-circle me-2"></i>
                        Help
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">Dashboard</h1>
                    <p class="text-muted mb-0">
                        Welcome back, <?= htmlspecialchars($user['name']) ?>
                        <?php if (isset($user['institute_name'])): ?>
                            - <?= htmlspecialchars($user['institute_name']) ?>
                        <?php endif; ?>
                    </p>
                </div>
                <div>
                    <span class="badge bg-primary fs-6"><?= htmlspecialchars($user['role']) ?></span>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <?php foreach ($data['stats'] as $key => $value): ?>
                    <?php if (is_numeric($value)): ?>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <div class="h2 text-primary mb-1"><?= number_format($value) ?></div>
                                    <div class="text-muted small"><?= ucwords(str_replace('_', ' ', $key)) ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
            
            <!-- Quick Actions -->
            <?php if (!empty($data['quick_actions'])): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-lightning me-2"></i>
                                    Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($data['quick_actions'] as $action): ?>
                                        <div class="col-md-6 col-lg-3 mb-3">
                                            <a href="<?= htmlspecialchars($action['url']) ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3 text-decoration-none">
                                                <i class="bi bi-<?= htmlspecialchars($action['icon']) ?> display-6 mb-2"></i>
                                                <span><?= htmlspecialchars($action['title']) ?></span>
                                            </a>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <!-- Notifications -->
                <?php if (!empty($data['notifications'])): ?>
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-bell me-2"></i>
                                    Notifications
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($data['notifications'] as $notification): ?>
                                    <div class="alert alert-<?= $notification['type'] === 'error' ? 'danger' : ($notification['type'] === 'warning' ? 'warning' : 'info') ?> alert-dismissible fade show" role="alert">
                                        <i class="bi bi-<?= $notification['type'] === 'error' ? 'exclamation-triangle' : ($notification['type'] === 'warning' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                                        <?= htmlspecialchars($notification['message']) ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Recent Activities -->
                <?php if (!empty($data['recent_activities'])): ?>
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-clock-history me-2"></i>
                                    Recent Activities
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($data['recent_activities'])): ?>
                                    <p class="text-muted text-center py-3">No recent activities</p>
                                <?php else: ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach (array_slice($data['recent_activities'], 0, 5) as $activity): ?>
                                            <div class="list-group-item border-0 px-0">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <div class="fw-medium"><?= htmlspecialchars($activity['action'] ?? 'Activity') ?></div>
                                                        <?php if (isset($activity['user_name'])): ?>
                                                            <small class="text-muted">by <?= htmlspecialchars($activity['user_name']) ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?= date('M j, H:i', strtotime($activity['created_at'])) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="/activity-logs" class="btn btn-sm btn-outline-primary">View All Activities</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Faculty Statistics (for Super Admin) -->
            <?php if ($user['role'] === 'Super Admin' && isset($data['stats']['faculty_stats'])): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-pie-chart me-2"></i>
                                    Faculty Distribution
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($data['stats']['faculty_stats'] as $faculty): ?>
                                        <div class="col-md-6 col-lg-3 mb-3">
                                            <div class="text-center">
                                                <div class="h4 text-primary"><?= $faculty['count'] ?></div>
                                                <div class="small text-muted"><?= htmlspecialchars($faculty['faculty']) ?></div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Help Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card bg-light border-0">
                        <div class="card-body text-center">
                            <i class="bi bi-question-circle text-primary display-4 mb-3"></i>
                            <h5>Need Help?</h5>
                            <p class="text-muted mb-3">
                                Contact the Quality Management & Benchmarking office for assistance.
                            </p>
                            <a href="mailto:<EMAIL>" class="btn btn-primary">
                                <i class="bi bi-envelope me-2"></i>
                                Contact Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
