<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title ?? 'MBGL AQAR Management System') ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --siu-primary: <?= $config['siu']['primary_color'] ?? '#DC143C' ?>;
            --siu-primary-dark: #B91C3C;
            --siu-secondary: #6B7280;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--siu-primary) !important;
        }
        
        .btn-primary {
            background-color: var(--siu-primary);
            border-color: var(--siu-primary);
        }
        
        .btn-primary:hover {
            background-color: var(--siu-primary-dark);
            border-color: var(--siu-primary-dark);
        }
        
        .text-primary {
            color: var(--siu-primary) !important;
        }
        
        .bg-primary {
            background-color: var(--siu-primary) !important;
        }
        
        .border-primary {
            border-color: var(--siu-primary) !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0.5rem;
        }
        
        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: var(--siu-primary);
        }
        
        .sidebar .nav-link.active {
            background-color: var(--siu-primary);
            color: white;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .footer {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            padding: 1rem 0;
            margin-top: auto;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: block;
        }
    </style>
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= htmlspecialchars($csrf_token ?? '') ?>">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-mortarboard-fill me-2"></i>
                MBGL AQAR Management System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if (isset($user) && $user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                <?= htmlspecialchars($user['name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><h6 class="dropdown-header"><?= htmlspecialchars($user['role']) ?></h6></li>
                                <?php if (isset($user['institute_name'])): ?>
                                    <li><small class="dropdown-item-text text-muted"><?= htmlspecialchars($user['institute_name']) ?></small></li>
                                    <li><hr class="dropdown-divider"></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item" href="/settings"><i class="bi bi-gear me-2"></i>Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/login">
                                <i class="bi bi-box-arrow-in-right me-1"></i>
                                Login
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="flex-grow-1">
        <?php if (isset($content)): ?>
            <?= $content ?>
        <?php else: ?>
            <!-- Content will be loaded here -->
        <?php endif; ?>
    </div>
    
    <!-- Footer -->
    <footer class="footer bg-light">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        © 2025 Symbiosis International University. All rights reserved.
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        MBGL AQAR Management System v1.0
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // CSRF Token for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Set up AJAX defaults
        if (typeof fetch !== 'undefined') {
            const originalFetch = fetch;
            fetch = function(url, options = {}) {
                if (options.method && options.method.toUpperCase() !== 'GET') {
                    options.headers = options.headers || {};
                    options.headers['X-CSRF-Token'] = csrfToken;
                }
                return originalFetch(url, options);
            };
        }
        
        // Loading indicator
        function showLoading() {
            document.querySelectorAll('.loading').forEach(el => el.classList.add('show'));
        }
        
        function hideLoading() {
            document.querySelectorAll('.loading').forEach(el => el.classList.remove('show'));
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            document.querySelectorAll('.alert').forEach(alert => {
                if (!alert.classList.contains('alert-permanent')) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }
            });
        }, 5000);
    </script>
    
    <!-- Additional scripts can be added here -->
    <?php if (isset($additionalScripts)): ?>
        <?= $additionalScripts ?>
    <?php endif; ?>
</body>
</html>
