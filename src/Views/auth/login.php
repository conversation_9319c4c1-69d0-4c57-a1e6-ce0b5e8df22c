<?php
ob_start();
?>

<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left side - Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary text-white">
            <div class="text-center">
                <i class="bi bi-mortarboard-fill display-1 mb-4"></i>
                <h1 class="display-4 fw-bold mb-3">MBGL AQAR</h1>
                <h2 class="h4 mb-4">Management System</h2>
                <p class="lead mb-4">
                    Comprehensive Annual Quality Assurance Report management for 
                    Symbiosis International University implementing the NAAC MBGL framework
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="h2 fw-bold">43</div>
                        <small>Institutes</small>
                    </div>
                    <div class="col-4">
                        <div class="h2 fw-bold">8</div>
                        <small>Faculties</small>
                    </div>
                    <div class="col-4">
                        <div class="h2 fw-bold">1000</div>
                        <small>Points</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right side - Login Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 400px;">
                <div class="text-center mb-4">
                    <i class="bi bi-shield-lock text-primary display-4 mb-3"></i>
                    <h2 class="h3 mb-3">Welcome Back</h2>
                    <p class="text-muted">Sign in to your MBGL AQAR account</p>
                </div>
                
                <!-- Error Messages -->
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php foreach ($errors as $error): ?>
                            <?= $error ?><br>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Success Messages -->
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <?php foreach ($success as $message): ?>
                            <?= $message ?><br>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Login Form -->
                <form method="POST" action="/login" class="needs-validation" novalidate>
                    <input type="hidden" name="_token" value="<?= htmlspecialchars($csrf_token) ?>">
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="bi bi-envelope me-1"></i>
                            SIU Email Address
                        </label>
                        <input type="email" 
                               class="form-control form-control-lg" 
                               id="email" 
                               name="email" 
                               placeholder="<EMAIL>"
                               required
                               value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                        <div class="invalid-feedback">
                            Please provide a valid SIU email address.
                        </div>
                        <small class="form-text text-muted">
                            Use your official SIU email address (e.g., @siu.edu.in, @sibmpune.edu.in)
                        </small>
                    </div>
                    
                    <div class="mb-4">
                        <label for="security_code" class="form-label">
                            <i class="bi bi-key me-1"></i>
                            6-Digit Security Code
                        </label>
                        <input type="text" 
                               class="form-control form-control-lg text-center" 
                               id="security_code" 
                               name="security_code" 
                               placeholder="123456"
                               maxlength="6"
                               pattern="[0-9]{6}"
                               required
                               style="letter-spacing: 0.5em;">
                        <div class="invalid-feedback">
                            Please enter your 6-digit security code.
                        </div>
                        <small class="form-text text-muted">
                            Enter the 6-digit security code provided by your institute
                        </small>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            Sign In
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <a href="/forgot-password" class="text-decoration-none">
                            <i class="bi bi-question-circle me-1"></i>
                            Forgot your security code?
                        </a>
                    </div>
                </form>
                
                <!-- Help Section -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="mb-2">
                        <i class="bi bi-info-circle text-primary me-1"></i>
                        Need Help?
                    </h6>
                    <small class="text-muted">
                        Contact your NAAC Coordinator or Quality Management & Benchmarking office at 
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Security code input formatting
document.getElementById('security_code').addEventListener('input', function(e) {
    // Only allow numbers
    this.value = this.value.replace(/[^0-9]/g, '');
    
    // Limit to 6 digits
    if (this.value.length > 6) {
        this.value = this.value.slice(0, 6);
    }
});

// Auto-focus next field on enter
document.getElementById('email').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('security_code').focus();
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
