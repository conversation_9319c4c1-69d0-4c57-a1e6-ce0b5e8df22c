<?php
/**
 * MBGL Configuration Dashboard
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0
 */

$this->layout('layouts/admin', ['title' => $page_title]);
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= htmlspecialchars($page_title) ?></h1>
            <p class="mb-0 text-muted">Configure MBGL framework attributes, sub-components, and validation rules</p>
        </div>
        <div class="btn-group">
            <a href="/mbgl-config/export?type=all" class="btn btn-outline-primary">
                <i class="fas fa-download"></i> Export All
            </a>
            <button type="button" class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                <span class="sr-only">Toggle Dropdown</span>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="/mbgl-config/export?type=attributes">Export Attributes</a></li>
                <li><a class="dropdown-item" href="/mbgl-config/export?type=sub_components">Export Sub-Components</a></li>
                <li><a class="dropdown-item" href="/mbgl-config/export?type=validation_rules">Export Validation Rules</a></li>
                <li><a class="dropdown-item" href="/mbgl-config/export?type=sdg_indicators">Export SDG Indicators</a></li>
                <li><a class="dropdown-item" href="/mbgl-config/export?type=programme_codes">Export Programme Codes</a></li>
            </ul>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Attributes</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= count($attributes) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Sub-Components</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= array_sum(array_column($attributes, 'sub_component_count')) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-puzzle-piece fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Max Points</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $total_max_points ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">SDG Indicators</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $sdg_count ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-globe fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <a href="/mbgl-config/attributes" class="btn btn-primary btn-block">
                                <i class="fas fa-list-alt"></i><br>
                                <small>Manage Attributes</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="/mbgl-config/sub-components" class="btn btn-success btn-block">
                                <i class="fas fa-puzzle-piece"></i><br>
                                <small>Sub-Components</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="/mbgl-config/validation-rules" class="btn btn-info btn-block">
                                <i class="fas fa-check-circle"></i><br>
                                <small>Validation Rules</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="/mbgl-config/sdg-indicators" class="btn btn-warning btn-block">
                                <i class="fas fa-globe"></i><br>
                                <small>SDG Indicators</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="/mbgl-config/programme-codes" class="btn btn-secondary btn-block">
                                <i class="fas fa-code"></i><br>
                                <small>Programme Codes</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="/mbgl-config/create-attribute" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-plus"></i><br>
                                <small>Add Attribute</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- MBGL Attributes Overview -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">MBGL Attributes (A1-A10)</h6>
                    <a href="/mbgl-config/attributes" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>Code</th>
                                    <th>Attribute Name</th>
                                    <th>Type</th>
                                    <th>Max Points</th>
                                    <th>Sub-Components</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($attributes as $attribute): ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-<?= getAttributeTypeBadgeClass($attribute['attribute_type']) ?>">
                                            <?= htmlspecialchars($attribute['attribute_code']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($attribute['attribute_name']) ?></strong>
                                        <?php if (!empty($attribute['description'])): ?>
                                        <br><small class="text-muted"><?= htmlspecialchars(substr($attribute['description'], 0, 100)) ?>...</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= getAttributeTypeBadgeClass($attribute['attribute_type']) ?>">
                                            <?= htmlspecialchars($attribute['attribute_type']) ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <strong><?= $attribute['max_points'] ?></strong>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge badge-info"><?= $attribute['sub_component_count'] ?></span>
                                        <?php if ($attribute['total_sub_component_points']): ?>
                                        <br><small class="text-muted"><?= $attribute['total_sub_component_points'] ?> pts</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="/mbgl-config/edit-attribute?id=<?= $attribute['id'] ?>" 
                                               class="btn btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/mbgl-config/sub-components?attribute_id=<?= $attribute['id'] ?>" 
                                               class="btn btn-outline-success" title="Sub-Components">
                                                <i class="fas fa-puzzle-piece"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Attribute Type Distribution -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Attribute Type Distribution</h6>
                </div>
                <div class="card-body">
                    <?php foreach ($attribute_stats as $stat): ?>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="badge badge-<?= getAttributeTypeBadgeClass($stat['attribute_type']) ?>">
                                <?= htmlspecialchars($stat['attribute_type']) ?>
                            </span>
                            <small class="text-muted"><?= $stat['count'] ?> attributes</small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-<?= getAttributeTypeBadgeClass($stat['attribute_type']) ?>" 
                                 style="width: <?= ($stat['count'] / count($attributes)) * 100 ?>%"></div>
                        </div>
                        <small class="text-muted"><?= $stat['total_points'] ?> total points</small>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Status</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-list-alt text-primary"></i> Total Attributes</span>
                            <span class="badge badge-primary"><?= count($attributes) ?></span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-puzzle-piece text-success"></i> Sub-Components</span>
                            <span class="badge badge-success"><?= array_sum(array_column($attributes, 'sub_component_count')) ?></span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-globe text-warning"></i> SDG Indicators</span>
                            <span class="badge badge-warning"><?= $sdg_count ?></span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-code text-secondary"></i> Programme Codes</span>
                            <span class="badge badge-secondary"><?= $programme_count ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
/**
 * Get badge class for attribute type
 */
function getAttributeTypeBadgeClass(string $type): string
{
    return match($type) {
        'INPUT' => 'primary',
        'PROCESS' => 'success',
        'OUTCOME' => 'info',
        'EXTENDED' => 'warning',
        default => 'secondary'
    };
}
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Auto-refresh statistics every 5 minutes
    setInterval(function() {
        // You can implement AJAX refresh here if needed
    }, 300000);
});
</script>
