<?php
/**
 * MBGL Attributes Management
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0
 */

$this->layout('layouts/admin', ['title' => $page_title]);
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= htmlspecialchars($page_title) ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/mbgl-config">MBGL Config</a></li>
                    <li class="breadcrumb-item active">Attributes</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/mbgl-config/create-attribute" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Attribute
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-6">
                    <label for="search" class="form-label">Search Attributes</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?= htmlspecialchars($search) ?>" 
                           placeholder="Search by code, name, or description...">
                </div>
                <div class="col-md-3">
                    <label for="type_filter" class="form-label">Filter by Type</label>
                    <select class="form-select" id="type_filter" name="type_filter">
                        <option value="">All Types</option>
                        <?php foreach ($attribute_types as $type): ?>
                        <option value="<?= $type ?>" <?= ($_GET['type_filter'] ?? '') === $type ? 'selected' : '' ?>>
                            <?= $type ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="/mbgl-config/attributes" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Type Distribution -->
    <?php if (!empty($type_distribution)): ?>
    <div class="row mb-4">
        <?php foreach ($type_distribution as $dist): ?>
        <div class="col-md-3 mb-3">
            <div class="card border-left-<?= getAttributeTypeBadgeClass($dist['attribute_type']) ?> shadow h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-<?= getAttributeTypeBadgeClass($dist['attribute_type']) ?> text-uppercase mb-1">
                                <?= $dist['attribute_type'] ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $dist['count'] ?> <small class="text-muted">(<?= $dist['percentage'] ?>%)</small>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Attributes Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                MBGL Attributes 
                <span class="badge badge-secondary"><?= count($attributes) ?></span>
            </h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-cog"></i> Actions
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/mbgl-config/export?type=attributes">
                        <i class="fas fa-download"></i> Export Attributes
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="reorderAttributes()">
                        <i class="fas fa-sort"></i> Reorder Attributes
                    </a></li>
                </ul>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($attributes)): ?>
            <div class="text-center py-5">
                <i class="fas fa-list-alt fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-500">No attributes found</h5>
                <p class="text-muted">
                    <?= $search ? 'No attributes match your search criteria.' : 'Start by creating your first MBGL attribute.' ?>
                </p>
                <?php if (!$search): ?>
                <a href="/mbgl-config/create-attribute" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create First Attribute
                </a>
                <?php endif; ?>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="attributesTable">
                    <thead class="thead-light">
                        <tr>
                            <th width="80">Code</th>
                            <th>Attribute Name</th>
                            <th width="100">Type</th>
                            <th width="100">Max Points</th>
                            <th width="120">Sub-Components</th>
                            <th width="80">Order</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="sortableAttributes">
                        <?php foreach ($attributes as $attribute): ?>
                        <tr data-id="<?= $attribute['id'] ?>" data-order="<?= $attribute['sort_order'] ?>">
                            <td>
                                <span class="badge badge-<?= getAttributeTypeBadgeClass($attribute['attribute_type']) ?> badge-lg">
                                    <?= htmlspecialchars($attribute['attribute_code']) ?>
                                </span>
                            </td>
                            <td>
                                <div>
                                    <strong><?= htmlspecialchars($attribute['attribute_name']) ?></strong>
                                    <?php if (!empty($attribute['description'])): ?>
                                    <br><small class="text-muted">
                                        <?= htmlspecialchars(substr($attribute['description'], 0, 120)) ?>
                                        <?= strlen($attribute['description']) > 120 ? '...' : '' ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-<?= getAttributeTypeBadgeClass($attribute['attribute_type']) ?>">
                                    <?= htmlspecialchars($attribute['attribute_type']) ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <strong class="text-primary"><?= $attribute['max_points'] ?></strong>
                            </td>
                            <td class="text-center">
                                <a href="/mbgl-config/sub-components?attribute_id=<?= $attribute['id'] ?>" 
                                   class="text-decoration-none">
                                    <span class="badge badge-info"><?= $attribute['sub_component_count'] ?? 0 ?></span>
                                    <?php if (isset($attribute['total_sub_component_points']) && $attribute['total_sub_component_points']): ?>
                                    <br><small class="text-muted"><?= $attribute['total_sub_component_points'] ?> pts</small>
                                    <?php endif; ?>
                                </a>
                            </td>
                            <td class="text-center">
                                <span class="badge badge-secondary"><?= $attribute['sort_order'] ?></span>
                                <div class="mt-1">
                                    <i class="fas fa-grip-vertical text-muted sortable-handle" 
                                       style="cursor: move;" title="Drag to reorder"></i>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="/mbgl-config/edit-attribute?id=<?= $attribute['id'] ?>" 
                                       class="btn btn-outline-primary" title="Edit Attribute">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="/mbgl-config/sub-components?attribute_id=<?= $attribute['id'] ?>" 
                                       class="btn btn-outline-success" title="Manage Sub-Components">
                                        <i class="fas fa-puzzle-piece"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmDelete(<?= $attribute['id'] ?>, '<?= htmlspecialchars($attribute['attribute_name']) ?>')"
                                            title="Delete Attribute">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>

            <!-- Pagination -->
            <?php if (isset($pagination) && $pagination): ?>
            <nav aria-label="Attributes pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($pagination['current_page'] > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $pagination['current_page'] - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                    <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                            <?= $i ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $pagination['current_page'] + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <div class="text-center text-muted">
                    Showing <?= $pagination['start'] ?> to <?= $pagination['end'] ?> of <?= $pagination['total'] ?> attributes
                </div>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the attribute <strong id="deleteAttributeName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This will also delete all associated sub-components and validation rules.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Attribute</button>
            </div>
        </div>
    </div>
</div>

<?php
function getAttributeTypeBadgeClass(string $type): string
{
    return match($type) {
        'INPUT' => 'primary',
        'PROCESS' => 'success',
        'OUTCOME' => 'info',
        'EXTENDED' => 'warning',
        default => 'secondary'
    };
}
?>

<script>
let deleteAttributeId = null;

function confirmDelete(id, name) {
    deleteAttributeId = id;
    document.getElementById('deleteAttributeName').textContent = name;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (deleteAttributeId) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/mbgl-config/delete-attribute';
        
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = deleteAttributeId;
        
        form.appendChild(idInput);
        document.body.appendChild(form);
        form.submit();
    }
});

// Initialize sortable for reordering
function reorderAttributes() {
    // Toggle sortable mode
    const tbody = document.getElementById('sortableAttributes');
    const handles = tbody.querySelectorAll('.sortable-handle');
    
    handles.forEach(handle => {
        handle.style.cursor = 'move';
        handle.style.color = '#007bff';
    });
    
    // You can implement drag-and-drop functionality here
    alert('Drag and drop functionality can be implemented using libraries like SortableJS');
}

// Auto-focus search input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});
</script>
