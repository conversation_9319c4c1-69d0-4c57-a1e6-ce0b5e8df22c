<?php

namespace SIU\MBGL\Core;

/**
 * Security Management Class
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
class Security
{
    private array $config;
    private Session $session;
    
    public function __construct()
    {
        $config = require_once __DIR__ . '/../../config/security.php';
        if (!is_array($config)) {
            $this->config = [];
        } else {
            $this->config = $config;
        }
        $this->session = Session::getInstance();
    }
    
    public function hashPassword(string $password): string
    {
        return password_hash(
            $password,
            $this->config['password']['algorithm'],
            $this->config['password']['options']
        );
    }
    
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
    
    public function generateCSRFToken(): string
    {
        if (!$this->session->has('csrf_token') || $this->isCSRFTokenExpired()) {
            $token = bin2hex(random_bytes(32));
            $this->session->set('csrf_token', $token);
            $this->session->set('csrf_token_time', time());
        }
        
        return $this->session->get('csrf_token');
    }
    
    public function validateCSRFToken(string $token): bool
    {
        $csrfEnabled = $this->config['csrf']['enabled'] ?? true;
        if (!$csrfEnabled) {
            return true;
        }

        $sessionToken = $this->session->get('csrf_token');

        if (!$sessionToken || $this->isCSRFTokenExpired()) {
            return false;
        }

        $isValid = hash_equals($sessionToken, $token);

        $regenerateOnUse = $this->config['csrf']['regenerate_on_use'] ?? true;
        if ($isValid && $regenerateOnUse) {
            $this->session->remove('csrf_token');
            $this->session->remove('csrf_token_time');
        }

        return $isValid;
    }
    
    private function isCSRFTokenExpired(): bool
    {
        $tokenTime = $this->session->get('csrf_token_time', 0);
        $expireTime = $this->config['csrf']['expire_time'] ?? 3600; // 1 hour default
        return (time() - $tokenTime) > $expireTime;
    }
    
    public function sanitizeInput(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    public function sanitizeArray(array $data): array
    {
        $sanitized = [];
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeArray($value);
            } else {
                $sanitized[$key] = $this->sanitizeInput((string)$value);
            }
        }
        return $sanitized;
    }
    
    public function validateSIUEmail(string $email): bool
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }

        $domain = substr(strrchr($email, "@"), 1);
        $allowedDomains = $this->config['siu_security']['email_domains'] ?? [
            'siu.edu.in',
            'sibmpune.edu.in',
            'siib.ac.in',
            'scmhrd.edu',
            'sims.edu',
            'sidtm.edu.in'
        ];

        return in_array($domain, $allowedDomains);
    }
    
    public function generateSecurityCode(): string
    {
        $length = $this->config['siu_security']['security_code_length'] ?? 6;
        return str_pad((string)random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }
    
    public function validateFileUpload(array $file): array
    {
        $errors = [];

        // Check if file was uploaded
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'File upload failed';
            return $errors;
        }

        // Check file size
        $maxSize = $this->config['file_upload']['max_size'] ?? 10485760; // 10MB default
        if ($file['size'] > $maxSize) {
            $errors[] = 'File size exceeds maximum allowed size';
        }

        // Check file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowedExtensions = $this->config['file_upload']['allowed_extensions'] ?? ['csv', 'pdf', 'xlsx', 'xls'];
        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = 'File type not allowed';
        }

        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        $allowedMimeTypes = $this->config['file_upload']['allowed_mime_types'] ?? [
            'text/csv',
            'application/csv',
            'text/plain',
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!in_array($mimeType, $allowedMimeTypes)) {
            $errors[] = 'Invalid file type';
        }

        return $errors;
    }
    
    public function checkRateLimit(string $key, int $maxAttempts, int $window): bool
    {
        $attempts = $this->session->get("rate_limit_{$key}", []);
        $now = time();
        
        // Remove old attempts outside the window
        $attempts = array_filter($attempts, fn($time) => ($now - $time) < $window);
        
        if (count($attempts) >= $maxAttempts) {
            return false;
        }
        
        $attempts[] = $now;
        $this->session->set("rate_limit_{$key}", $attempts);
        
        return true;
    }
    
    public function encrypt(string $data): string
    {
        $key = $this->config['encryption']['key'] ?? 'default-key-32-characters-long';
        $cipher = $this->config['encryption']['cipher'] ?? 'AES-256-CBC';

        $iv = random_bytes(openssl_cipher_iv_length($cipher));
        $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);

        return base64_encode($iv . $encrypted);
    }

    public function decrypt(string $data): string
    {
        $key = $this->config['encryption']['key'] ?? 'default-key-32-characters-long';
        $cipher = $this->config['encryption']['cipher'] ?? 'AES-256-CBC';

        $data = base64_decode($data);
        $ivLength = openssl_cipher_iv_length($cipher);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);

        return openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
    }
    
    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }
    
    public function logSecurityEvent(string $event, array $details = []): void
    {
        $logData = [
            'event' => $event,
            'details' => json_encode($details),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log to file
        $logFile = __DIR__ . '/../../storage/logs/security.log';
        $logEntry = date('Y-m-d H:i:s') . " - {$event} - " . json_encode($logData) . PHP_EOL;
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}
