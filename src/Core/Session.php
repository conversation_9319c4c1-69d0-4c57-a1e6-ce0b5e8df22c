<?php

namespace SIU\MBGL\Core;

/**
 * Session Management Class
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
class Session
{
    private static ?Session $instance = null;
    private array $config;
    private bool $started = false;
    
    private function __construct()
    {
        $this->config = require_once __DIR__ . '/../../config/app.php';
        $this->start();
    }
    
    public static function getInstance(): Session
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function start(): void
    {
        if ($this->started || session_status() === PHP_SESSION_ACTIVE) {
            return;
        }
        
        $sessionConfig = $this->config['session'];
        
        // Configure session settings
        ini_set('session.name', $sessionConfig['name']);
        ini_set('session.gc_maxlifetime', $sessionConfig['lifetime']);
        ini_set('session.cookie_lifetime', $sessionConfig['lifetime']);
        ini_set('session.cookie_path', $sessionConfig['path']);
        ini_set('session.cookie_httponly', $sessionConfig['httponly']);
        ini_set('session.cookie_secure', $sessionConfig['secure']);
        ini_set('session.cookie_samesite', $sessionConfig['samesite']);
        ini_set('session.use_strict_mode', 1);
        
        session_start();
        $this->started = true;
        
        // Regenerate session ID periodically for security
        if (!$this->has('last_regeneration')) {
            $this->regenerateId();
        } elseif (time() - $this->get('last_regeneration') > 300) { // 5 minutes
            $this->regenerateId();
        }
        
        // Check for session timeout
        $this->checkTimeout();
        
        // Validate session security
        $this->validateSession();
    }
    
    public function set(string $key, $value): void
    {
        $_SESSION[$key] = $value;
    }
    
    public function get(string $key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }
    
    public function has(string $key): bool
    {
        return isset($_SESSION[$key]);
    }
    
    public function remove(string $key): void
    {
        unset($_SESSION[$key]);
    }
    
    public function clear(): void
    {
        $_SESSION = [];
    }
    
    public function destroy(): void
    {
        if ($this->started) {
            session_destroy();
            $this->started = false;
        }
    }
    
    public function regenerateId(): void
    {
        session_regenerate_id(true);
        $this->set('last_regeneration', time());
        $this->set('session_ip', $_SERVER['REMOTE_ADDR'] ?? '');
        $this->set('session_user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
    }
    
    public function setFlash(string $type, string $message): void
    {
        if (!$this->has('flash_messages')) {
            $this->set('flash_messages', []);
        }
        
        $flashMessages = $this->get('flash_messages');
        $flashMessages[$type][] = $message;
        $this->set('flash_messages', $flashMessages);
    }
    
    public function getFlash(?string $type = null): array
    {
        $flashMessages = $this->get('flash_messages', []);
        
        if ($type) {
            $messages = $flashMessages[$type] ?? [];
            unset($flashMessages[$type]);
        } else {
            $messages = $flashMessages;
            $flashMessages = [];
        }
        
        $this->set('flash_messages', $flashMessages);
        return $messages;
    }
    
    public function hasFlash(?string $type = null): bool
    {
        $flashMessages = $this->get('flash_messages', []);
        
        if ($type) {
            return !empty($flashMessages[$type]);
        }
        
        return !empty($flashMessages);
    }
    
    private function checkTimeout(): void
    {
        $timeout = $this->config['session']['lifetime'];
        $lastActivity = $this->get('last_activity', time());
        
        if (time() - $lastActivity > $timeout) {
            $this->destroy();
            return;
        }
        
        $this->set('last_activity', time());
    }
    
    private function validateSession(): void
    {
        $securityConfig = require_once __DIR__ . '/../../config/security.php';
        
        if ($securityConfig['session']['check_ip']) {
            $currentIp = $_SERVER['REMOTE_ADDR'] ?? '';
            $sessionIp = $this->get('session_ip', '');
            
            if ($sessionIp && $sessionIp !== $currentIp) {
                $this->destroy();
                return;
            }
        }
        
        if ($securityConfig['session']['check_user_agent']) {
            $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $sessionUserAgent = $this->get('session_user_agent', '');
            
            if ($sessionUserAgent && $sessionUserAgent !== $currentUserAgent) {
                $this->destroy();
                return;
            }
        }
    }
    
    public function isLoggedIn(): bool
    {
        return $this->has('user') && !empty($this->get('user'));
    }
    
    public function getUser(): ?array
    {
        return $this->get('user');
    }
    
    public function setUser(array $user): void
    {
        $this->set('user', $user);
        $this->set('login_time', time());
    }
    
    public function logout(): void
    {
        $this->clear();
        $this->destroy();
    }
    
    private function __clone() {}
    public function __wakeup() {}
}
