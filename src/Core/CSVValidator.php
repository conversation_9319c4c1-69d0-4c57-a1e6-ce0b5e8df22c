<?php

namespace SIU\MBGL\Core;

use SIU\MBGL\Models\ValidationRule;
use SIU\MBGL\Models\SubComponent;
use SIU\MBGL\Models\SDGIndicator;
use SIU\MBGL\Models\ProgrammeCode;
use SIU\MBGL\Models\Institute;

/**
 * Advanced CSV Validator
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Comprehensive CSV validation engine with complex validation patterns,
 * reference table validations, and SDG indicator integration
 * 
 * <AUTHOR> <PERSON><PERSON>dra <PERSON>de<PERSON>
 * @version 1.0
 */
class CSVValidator
{
    private ValidationRule $validationRuleModel;
    private SubComponent $subComponentModel;
    private SDGIndicator $sdgIndicatorModel;
    private ProgrammeCode $programmeCodeModel;
    private Institute $instituteModel;
    
    private array $validationErrors = [];
    private array $validationWarnings = [];
    private array $validationStats = [];
    private array $referenceCache = [];
    
    public function __construct()
    {
        $this->validationRuleModel = new ValidationRule();
        $this->subComponentModel = new SubComponent();
        $this->sdgIndicatorModel = new SDGIndicator();
        $this->programmeCodeModel = new ProgrammeCode();
        $this->instituteModel = new Institute();
    }
    
    /**
     * Validate CSV data against sub-component rules
     */
    public function validateCSVData(int $subComponentId, array $csvData, array $options = []): array
    {
        $this->resetValidation();
        
        // Get sub-component details
        $subComponent = $this->subComponentModel->getSubComponentWithAttribute($subComponentId);
        if (!$subComponent) {
            throw new \Exception('Sub-component not found');
        }
        
        // Get validation rules
        $validationRules = $this->validationRuleModel->findBySubComponent($subComponentId);
        if (empty($validationRules)) {
            return $this->buildValidationResult(true, 'No validation rules defined');
        }
        
        // Validate headers
        $headerValidation = $this->validateHeaders($csvData, $validationRules);
        if (!$headerValidation['valid']) {
            return $headerValidation;
        }
        
        // Validate data rows
        $this->validateDataRows($csvData, $validationRules, $options);
        
        // Generate statistics
        $this->generateValidationStats($csvData, $validationRules);
        
        return $this->buildValidationResult();
    }
    
    /**
     * Validate CSV headers against expected columns
     */
    private function validateHeaders(array $csvData, array $validationRules): array
    {
        if (empty($csvData)) {
            return $this->buildValidationResult(false, 'CSV file is empty');
        }
        
        $headers = array_keys($csvData[0]);
        $expectedColumns = array_column($validationRules, 'column_name');
        $requiredColumns = array_column(
            array_filter($validationRules, fn($rule) => $rule['is_required']),
            'column_name'
        );
        
        // Check for missing required columns
        $missingRequired = array_diff($requiredColumns, $headers);
        if (!empty($missingRequired)) {
            $this->validationErrors[] = [
                'type' => 'header_error',
                'message' => 'Missing required columns: ' . implode(', ', $missingRequired),
                'columns' => $missingRequired
            ];
        }
        
        // Check for unexpected columns
        $unexpectedColumns = array_diff($headers, $expectedColumns);
        if (!empty($unexpectedColumns)) {
            $this->validationWarnings[] = [
                'type' => 'header_warning',
                'message' => 'Unexpected columns found: ' . implode(', ', $unexpectedColumns),
                'columns' => $unexpectedColumns
            ];
        }
        
        return $this->buildValidationResult(empty($missingRequired));
    }
    
    /**
     * Validate data rows
     */
    private function validateDataRows(array $csvData, array $validationRules, array $options): void
    {
        $rulesByColumn = [];
        foreach ($validationRules as $rule) {
            $rulesByColumn[$rule['column_name']] = $rule;
        }
        
        foreach ($csvData as $rowIndex => $row) {
            $this->validateRow($row, $rulesByColumn, $rowIndex + 1, $options);
        }
    }
    
    /**
     * Validate single row
     */
    private function validateRow(array $row, array $rulesByColumn, int $rowNumber, array $options): void
    {
        foreach ($rulesByColumn as $columnName => $rule) {
            $value = $row[$columnName] ?? null;
            $this->validateField($value, $rule, $rowNumber, $columnName, $options);
        }
        
        // Cross-field validations
        $this->validateCrossFields($row, $rulesByColumn, $rowNumber, $options);
    }
    
    /**
     * Validate individual field
     */
    private function validateField($value, array $rule, int $rowNumber, string $columnName, array $options): void
    {
        // Check required fields
        if ($rule['is_required'] && $this->isEmpty($value)) {
            $this->addFieldError($rowNumber, $columnName, 
                $rule['error_message'] ?: "Field '{$rule['display_name']}' is required");
            return;
        }
        
        // Skip validation if value is empty and not required
        if ($this->isEmpty($value) && !$rule['is_required']) {
            return;
        }
        
        // Data type validation
        if (!$this->validateDataType($value, $rule['data_type'])) {
            $this->addFieldError($rowNumber, $columnName, 
                "Invalid data type for '{$rule['display_name']}'. Expected: {$rule['data_type']}");
            return;
        }
        
        // Specific validation based on validation type
        $this->performSpecificValidation($value, $rule, $rowNumber, $columnName, $options);
    }
    
    /**
     * Perform specific validation based on validation type
     */
    private function performSpecificValidation($value, array $rule, int $rowNumber, string $columnName, array $options): void
    {
        switch ($rule['validation_type']) {
            case 'regex':
                $this->validateRegex($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'reference':
                $this->validateReference($value, $rule, $rowNumber, $columnName, $options);
                break;
                
            case 'range':
                $this->validateRange($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'exact_match':
                $this->validateExactMatch($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'email':
                $this->validateEmail($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'date':
                $this->validateDate($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'url':
                $this->validateURL($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'phone':
                $this->validatePhone($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'academic_year':
                $this->validateAcademicYear($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'percentage':
                $this->validatePercentage($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'currency':
                $this->validateCurrency($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'sdg_indicators':
                $this->validateSDGIndicators($value, $rule, $rowNumber, $columnName);
                break;
                
            case 'programme_codes':
                $this->validateProgrammeCodes($value, $rule, $rowNumber, $columnName, $options);
                break;
                
            case 'custom':
                $this->validateCustom($value, $rule, $rowNumber, $columnName, $options);
                break;
        }
    }
    
    /**
     * Validate regex pattern
     */
    private function validateRegex($value, array $rule, int $rowNumber, string $columnName): void
    {
        if (!preg_match('/' . $rule['validation_params'] . '/', $value)) {
            $this->addFieldError($rowNumber, $columnName, 
                $rule['error_message'] ?: "Value does not match required pattern");
        }
    }
    
    /**
     * Validate reference table value
     */
    private function validateReference($value, array $rule, int $rowNumber, string $columnName, array $options): void
    {
        $referenceTable = $rule['reference_table'];
        if (!$referenceTable) {
            return;
        }
        
        // Use cache to avoid repeated database queries
        $cacheKey = $referenceTable . '_' . $value;
        if (isset($this->referenceCache[$cacheKey])) {
            $isValid = $this->referenceCache[$cacheKey];
        } else {
            $isValid = $this->checkReferenceValue($value, $referenceTable, $options);
            $this->referenceCache[$cacheKey] = $isValid;
        }
        
        if (!$isValid) {
            $this->addFieldError($rowNumber, $columnName, 
                $rule['error_message'] ?: "Invalid reference value: {$value}");
        }
    }
    
    /**
     * Check reference value in database
     */
    private function checkReferenceValue($value, string $referenceTable, array $options): bool
    {
        switch ($referenceTable) {
            case 'institutes':
                return $this->instituteModel->findBy('institute_code', $value) !== null;
                
            case 'programme_codes':
                $instituteId = $options['institute_id'] ?? null;
                if ($instituteId) {
                    return $this->programmeCodeModel->findBy('programme_code', $value, ['institute_id' => $instituteId]) !== null;
                }
                return $this->programmeCodeModel->findBy('programme_code', $value) !== null;
                
            case 'sdg_indicators':
                return $this->sdgIndicatorModel->findBy('indicator_name', $value) !== null;
                
            default:
                return true;
        }
    }
    
    /**
     * Validate range values
     */
    private function validateRange($value, array $rule, int $rowNumber, string $columnName): void
    {
        if (!is_numeric($value)) {
            $this->addFieldError($rowNumber, $columnName, "Value must be numeric for range validation");
            return;
        }
        
        $params = $rule['validation_params'];
        $numericValue = (float)$value;
        
        // Parse range parameters
        if (strpos($params, '-') !== false) {
            [$min, $max] = explode('-', $params);
            if ($numericValue < (float)$min || $numericValue > (float)$max) {
                $this->addFieldError($rowNumber, $columnName, 
                    "Value must be between {$min} and {$max}");
            }
        } elseif (strpos($params, ',') !== false) {
            $rangeParts = [];
            foreach (explode(',', $params) as $part) {
                [$key, $val] = explode(':', $part);
                $rangeParts[trim($key)] = (float)trim($val);
            }
            
            if (isset($rangeParts['min']) && $numericValue < $rangeParts['min']) {
                $this->addFieldError($rowNumber, $columnName, 
                    "Value must be at least {$rangeParts['min']}");
            }
            
            if (isset($rangeParts['max']) && $numericValue > $rangeParts['max']) {
                $this->addFieldError($rowNumber, $columnName, 
                    "Value must not exceed {$rangeParts['max']}");
            }
        }
    }
    
    /**
     * Validate exact match
     */
    private function validateExactMatch($value, array $rule, int $rowNumber, string $columnName): void
    {
        $expectedValues = explode(',', $rule['validation_params']);
        $expectedValues = array_map('trim', $expectedValues);
        
        if (!in_array($value, $expectedValues)) {
            $this->addFieldError($rowNumber, $columnName, 
                "Value must be one of: " . implode(', ', $expectedValues));
        }
    }
    
    /**
     * Validate email format
     */
    private function validateEmail($value, array $rule, int $rowNumber, string $columnName): void
    {
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $this->addFieldError($rowNumber, $columnName, "Invalid email format");
        }
        
        // Check SIU domain if specified
        if (!empty($rule['validation_params'])) {
            $allowedDomains = explode(',', $rule['validation_params']);
            $domain = substr(strrchr($value, "@"), 1);
            
            if (!in_array($domain, $allowedDomains)) {
                $this->addFieldError($rowNumber, $columnName, 
                    "Email must be from allowed domains: " . implode(', ', $allowedDomains));
            }
        }
    }
    
    /**
     * Validate date format
     */
    private function validateDate($value, array $rule, int $rowNumber, string $columnName): void
    {
        $format = $rule['validation_params'] ?: 'Y-m-d';
        $date = \DateTime::createFromFormat($format, $value);
        
        if (!$date || $date->format($format) !== $value) {
            $this->addFieldError($rowNumber, $columnName, 
                "Invalid date format. Expected: {$format}");
        }
    }
    
    /**
     * Validate URL format
     */
    private function validateURL($value, array $rule, int $rowNumber, string $columnName): void
    {
        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            $this->addFieldError($rowNumber, $columnName, "Invalid URL format");
        }
    }
    
    /**
     * Validate phone number
     */
    private function validatePhone($value, array $rule, int $rowNumber, string $columnName): void
    {
        // Indian phone number validation
        $pattern = '/^(\+91|91|0)?[6-9]\d{9}$/';
        if (!preg_match($pattern, $value)) {
            $this->addFieldError($rowNumber, $columnName, "Invalid Indian phone number format");
        }
    }
    
    /**
     * Validate academic year format
     */
    private function validateAcademicYear($value, array $rule, int $rowNumber, string $columnName): void
    {
        // Format: 2023-24 or 2023-2024
        $pattern = '/^20\d{2}-(20)?\d{2}$/';
        if (!preg_match($pattern, $value)) {
            $this->addFieldError($rowNumber, $columnName, 
                "Invalid academic year format. Expected: YYYY-YY or YYYY-YYYY");
        }
    }
    
    /**
     * Validate percentage value
     */
    private function validatePercentage($value, array $rule, int $rowNumber, string $columnName): void
    {
        if (!is_numeric($value)) {
            $this->addFieldError($rowNumber, $columnName, "Percentage must be numeric");
            return;
        }
        
        $numericValue = (float)$value;
        if ($numericValue < 0 || $numericValue > 100) {
            $this->addFieldError($rowNumber, $columnName, "Percentage must be between 0 and 100");
        }
    }
    
    /**
     * Validate currency value
     */
    private function validateCurrency($value, array $rule, int $rowNumber, string $columnName): void
    {
        // Remove currency symbols and commas
        $cleanValue = preg_replace('/[₹,\s]/', '', $value);
        
        if (!is_numeric($cleanValue)) {
            $this->addFieldError($rowNumber, $columnName, "Invalid currency format");
            return;
        }
        
        $numericValue = (float)$cleanValue;
        if ($numericValue < 0) {
            $this->addFieldError($rowNumber, $columnName, "Currency value cannot be negative");
        }
    }
    
    /**
     * Validate SDG indicators
     */
    private function validateSDGIndicators($value, array $rule, int $rowNumber, string $columnName): void
    {
        if (empty($value)) {
            return;
        }
        
        $indicators = array_map('trim', explode(',', $value));
        $validIndicators = $this->getValidSDGIndicators();
        
        foreach ($indicators as $indicator) {
            if (!in_array($indicator, $validIndicators)) {
                $this->addFieldError($rowNumber, $columnName, 
                    "Invalid SDG indicator: {$indicator}");
            }
        }
    }
    
    /**
     * Validate programme codes
     */
    private function validateProgrammeCodes($value, array $rule, int $rowNumber, string $columnName, array $options): void
    {
        if (empty($value)) {
            return;
        }
        
        $codes = array_map('trim', explode(',', $value));
        $instituteId = $options['institute_id'] ?? null;
        
        foreach ($codes as $code) {
            if (!$this->isValidProgrammeCode($code, $instituteId)) {
                $this->addFieldError($rowNumber, $columnName, 
                    "Invalid programme code: {$code}");
            }
        }
    }
    
    /**
     * Custom validation logic
     */
    private function validateCustom($value, array $rule, int $rowNumber, string $columnName, array $options): void
    {
        // Implement custom validation logic based on validation_params
        $customRule = $rule['validation_params'];
        
        // Example custom validations
        switch ($customRule) {
            case 'unique_in_column':
                // This would require tracking values across rows
                break;
                
            case 'sum_equals_100':
                // This would require cross-field validation
                break;
                
            default:
                // Custom validation can be extended here
                break;
        }
    }
    
    /**
     * Cross-field validations
     */
    private function validateCrossFields(array $row, array $rulesByColumn, int $rowNumber, array $options): void
    {
        // Example: Validate that start date is before end date
        if (isset($row['start_date']) && isset($row['end_date'])) {
            $startDate = strtotime($row['start_date']);
            $endDate = strtotime($row['end_date']);
            
            if ($startDate && $endDate && $startDate > $endDate) {
                $this->addFieldError($rowNumber, 'end_date', 
                    "End date must be after start date");
            }
        }
        
        // Example: Validate percentage fields sum to 100
        $percentageFields = ['male_percentage', 'female_percentage'];
        $total = 0;
        $hasPercentageFields = false;
        
        foreach ($percentageFields as $field) {
            if (isset($row[$field]) && is_numeric($row[$field])) {
                $total += (float)$row[$field];
                $hasPercentageFields = true;
            }
        }
        
        if ($hasPercentageFields && abs($total - 100) > 0.01) {
            $this->addFieldError($rowNumber, 'male_percentage', 
                "Male and female percentages must sum to 100%");
        }
    }
    
    /**
     * Helper methods
     */
    private function isEmpty($value): bool
    {
        return $value === null || $value === '' || $value === [];
    }
    
    private function validateDataType($value, string $dataType): bool
    {
        switch ($dataType) {
            case 'integer':
                return filter_var($value, FILTER_VALIDATE_INT) !== false;
            case 'float':
            case 'decimal':
                return is_numeric($value);
            case 'boolean':
                return in_array(strtolower($value), ['true', 'false', '1', '0', 'yes', 'no']);
            case 'string':
            case 'text':
                return is_string($value);
            case 'date':
                return strtotime($value) !== false;
            default:
                return true;
        }
    }
    
    private function addFieldError(int $rowNumber, string $columnName, string $message): void
    {
        $this->validationErrors[] = [
            'type' => 'field_error',
            'row' => $rowNumber,
            'column' => $columnName,
            'message' => $message
        ];
    }
    
    private function getValidSDGIndicators(): array
    {
        if (!isset($this->referenceCache['sdg_indicators'])) {
            $indicators = $this->sdgIndicatorModel->getActiveIndicators();
            $this->referenceCache['sdg_indicators'] = array_column($indicators, 'indicator_name');
        }
        
        return $this->referenceCache['sdg_indicators'];
    }
    
    private function isValidProgrammeCode(string $code, ?int $instituteId): bool
    {
        $cacheKey = "programme_code_{$code}_{$instituteId}";
        
        if (!isset($this->referenceCache[$cacheKey])) {
            if ($instituteId) {
                $programme = $this->programmeCodeModel->findBy('programme_code', $code, ['institute_id' => $instituteId]);
            } else {
                $programme = $this->programmeCodeModel->findBy('programme_code', $code);
            }
            
            $this->referenceCache[$cacheKey] = $programme !== null;
        }
        
        return $this->referenceCache[$cacheKey];
    }
    
    private function resetValidation(): void
    {
        $this->validationErrors = [];
        $this->validationWarnings = [];
        $this->validationStats = [];
    }
    
    private function generateValidationStats(array $csvData, array $validationRules): void
    {
        $this->validationStats = [
            'total_rows' => count($csvData),
            'total_columns' => count($validationRules),
            'error_count' => count($this->validationErrors),
            'warning_count' => count($this->validationWarnings),
            'success_rate' => count($csvData) > 0 ? 
                ((count($csvData) - count(array_unique(array_column($this->validationErrors, 'row')))) / count($csvData)) * 100 : 0
        ];
    }
    
    private function buildValidationResult(bool $isValid = null, string $message = null): array
    {
        if ($isValid === null) {
            $isValid = empty($this->validationErrors);
        }
        
        return [
            'valid' => $isValid,
            'message' => $message ?: ($isValid ? 'Validation successful' : 'Validation failed'),
            'errors' => $this->validationErrors,
            'warnings' => $this->validationWarnings,
            'statistics' => $this->validationStats
        ];
    }
}
