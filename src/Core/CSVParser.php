<?php

namespace SIU\MBGL\Core;

/**
 * Advanced CSV Parser
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Handles CSV file parsing, encoding detection, and data preprocessing
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0
 */
class CSVParser
{
    private array $config;
    private array $parseErrors = [];
    private array $parseWarnings = [];
    private array $parseStats = [];
    
    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'delimiter' => ',',
            'enclosure' => '"',
            'escape' => '\\',
            'max_file_size' => 10 * 1024 * 1024, // 10MB
            'max_rows' => 10000,
            'encoding' => 'UTF-8',
            'auto_detect_encoding' => true,
            'trim_values' => true,
            'skip_empty_rows' => true,
            'validate_headers' => true,
            'normalize_headers' => true
        ], $config);
    }
    
    /**
     * Parse CSV file from uploaded file
     */
    public function parseFile(string $filePath): array
    {
        $this->resetParser();
        
        // Validate file
        $fileValidation = $this->validateFile($filePath);
        if (!$fileValidation['valid']) {
            return $fileValidation;
        }
        
        // Detect encoding
        $encoding = $this->detectEncoding($filePath);
        
        // Parse CSV content
        $csvData = $this->parseCSVContent($filePath, $encoding);
        
        // Post-process data
        $processedData = $this->postProcessData($csvData);
        
        // Generate statistics
        $this->generateParseStats($processedData);
        
        return $this->buildParseResult($processedData);
    }
    
    /**
     * Parse CSV from string content
     */
    public function parseString(string $csvContent): array
    {
        $this->resetParser();
        
        // Create temporary file
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_parse_');
        file_put_contents($tempFile, $csvContent);
        
        try {
            $result = $this->parseFile($tempFile);
        } finally {
            unlink($tempFile);
        }
        
        return $result;
    }
    
    /**
     * Validate uploaded file
     */
    private function validateFile(string $filePath): array
    {
        // Check if file exists
        if (!file_exists($filePath)) {
            return $this->buildParseResult([], false, 'File not found');
        }
        
        // Check file size
        $fileSize = filesize($filePath);
        if ($fileSize > $this->config['max_file_size']) {
            return $this->buildParseResult([], false, 
                'File size exceeds maximum allowed size of ' . 
                $this->formatBytes($this->config['max_file_size']));
        }
        
        // Check if file is readable
        if (!is_readable($filePath)) {
            return $this->buildParseResult([], false, 'File is not readable');
        }
        
        // Check file extension
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if (!in_array($extension, ['csv', 'txt'])) {
            $this->parseWarnings[] = [
                'type' => 'file_extension',
                'message' => "Unexpected file extension: {$extension}. Expected CSV or TXT."
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Detect file encoding
     */
    private function detectEncoding(string $filePath): string
    {
        if (!$this->config['auto_detect_encoding']) {
            return $this->config['encoding'];
        }
        
        $content = file_get_contents($filePath, false, null, 0, 8192); // Read first 8KB
        
        // Common encodings to check
        $encodings = ['UTF-8', 'UTF-16', 'UTF-16BE', 'UTF-16LE', 'ISO-8859-1', 'Windows-1252'];
        
        foreach ($encodings as $encoding) {
            if (mb_check_encoding($content, $encoding)) {
                if ($encoding !== 'UTF-8') {
                    $this->parseWarnings[] = [
                        'type' => 'encoding_detection',
                        'message' => "File encoding detected as {$encoding}. Converting to UTF-8."
                    ];
                }
                return $encoding;
            }
        }
        
        // Fallback to configured encoding
        $this->parseWarnings[] = [
            'type' => 'encoding_fallback',
            'message' => "Could not detect encoding. Using fallback: {$this->config['encoding']}"
        ];
        
        return $this->config['encoding'];
    }
    
    /**
     * Parse CSV content
     */
    private function parseCSVContent(string $filePath, string $encoding): array
    {
        $csvData = [];
        $headers = [];
        $rowCount = 0;
        
        // Open file with proper encoding
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            throw new \Exception('Could not open CSV file');
        }
        
        try {
            // Read and process each line
            while (($row = fgetcsv($handle, 0, $this->config['delimiter'], 
                                  $this->config['enclosure'], $this->config['escape'])) !== false) {
                
                $rowCount++;
                
                // Check row limit
                if ($rowCount > $this->config['max_rows']) {
                    $this->parseWarnings[] = [
                        'type' => 'row_limit',
                        'message' => "File contains more than {$this->config['max_rows']} rows. Only first {$this->config['max_rows']} rows will be processed."
                    ];
                    break;
                }
                
                // Convert encoding if needed
                if ($encoding !== 'UTF-8') {
                    $row = array_map(function($value) use ($encoding) {
                        return mb_convert_encoding($value, 'UTF-8', $encoding);
                    }, $row);
                }
                
                // Trim values if configured
                if ($this->config['trim_values']) {
                    $row = array_map('trim', $row);
                }
                
                // Handle headers
                if (empty($headers)) {
                    $headers = $this->processHeaders($row);
                    continue;
                }
                
                // Skip empty rows if configured
                if ($this->config['skip_empty_rows'] && $this->isEmptyRow($row)) {
                    continue;
                }
                
                // Validate row length
                if (count($row) !== count($headers)) {
                    $this->parseWarnings[] = [
                        'type' => 'column_mismatch',
                        'row' => $rowCount,
                        'message' => "Row {$rowCount} has " . count($row) . " columns, expected " . count($headers)
                    ];
                    
                    // Pad or truncate row to match headers
                    $row = $this->normalizeRowLength($row, count($headers));
                }
                
                // Create associative array
                $csvData[] = array_combine($headers, $row);
            }
            
        } finally {
            fclose($handle);
        }
        
        return $csvData;
    }
    
    /**
     * Process and normalize headers
     */
    private function processHeaders(array $headers): array
    {
        if ($this->config['normalize_headers']) {
            $headers = array_map(function($header) {
                // Remove BOM if present
                $header = preg_replace('/^\xEF\xBB\xBF/', '', $header);
                
                // Normalize whitespace
                $header = preg_replace('/\s+/', ' ', trim($header));
                
                // Convert to lowercase and replace spaces with underscores
                $header = strtolower(str_replace(' ', '_', $header));
                
                // Remove special characters except underscores
                $header = preg_replace('/[^a-z0-9_]/', '', $header);
                
                return $header;
            }, $headers);
        }
        
        // Check for duplicate headers
        $duplicates = array_diff_assoc($headers, array_unique($headers));
        if (!empty($duplicates)) {
            $this->parseErrors[] = [
                'type' => 'duplicate_headers',
                'message' => 'Duplicate column headers found: ' . implode(', ', array_unique($duplicates))
            ];
        }
        
        // Check for empty headers
        $emptyHeaders = array_filter($headers, function($header) {
            return empty($header);
        });
        
        if (!empty($emptyHeaders)) {
            $this->parseWarnings[] = [
                'type' => 'empty_headers',
                'message' => 'Empty column headers found at positions: ' . implode(', ', array_keys($emptyHeaders))
            ];
            
            // Generate default names for empty headers
            foreach ($headers as $index => $header) {
                if (empty($header)) {
                    $headers[$index] = 'column_' . ($index + 1);
                }
            }
        }
        
        return $headers;
    }
    
    /**
     * Check if row is empty
     */
    private function isEmptyRow(array $row): bool
    {
        return empty(array_filter($row, function($value) {
            return !empty(trim($value));
        }));
    }
    
    /**
     * Normalize row length to match headers
     */
    private function normalizeRowLength(array $row, int $expectedLength): array
    {
        $currentLength = count($row);
        
        if ($currentLength < $expectedLength) {
            // Pad with empty strings
            $row = array_pad($row, $expectedLength, '');
        } elseif ($currentLength > $expectedLength) {
            // Truncate
            $row = array_slice($row, 0, $expectedLength);
        }
        
        return $row;
    }
    
    /**
     * Post-process parsed data
     */
    private function postProcessData(array $csvData): array
    {
        if (empty($csvData)) {
            return $csvData;
        }
        
        // Data type inference and conversion
        $csvData = $this->inferAndConvertDataTypes($csvData);
        
        // Remove completely empty rows
        $csvData = array_filter($csvData, function($row) {
            return !empty(array_filter($row, function($value) {
                return $value !== '' && $value !== null;
            }));
        });
        
        // Re-index array
        $csvData = array_values($csvData);
        
        return $csvData;
    }
    
    /**
     * Infer and convert data types
     */
    private function inferAndConvertDataTypes(array $csvData): array
    {
        if (empty($csvData)) {
            return $csvData;
        }
        
        $headers = array_keys($csvData[0]);
        $typeInference = [];
        
        // Analyze first few rows to infer types
        $sampleSize = min(100, count($csvData));
        
        foreach ($headers as $header) {
            $values = array_slice(array_column($csvData, $header), 0, $sampleSize);
            $typeInference[$header] = $this->inferColumnType($values);
        }
        
        // Convert values based on inferred types
        foreach ($csvData as &$row) {
            foreach ($row as $header => &$value) {
                if ($value === '' || $value === null) {
                    continue;
                }
                
                switch ($typeInference[$header]) {
                    case 'integer':
                        if (is_numeric($value) && (int)$value == $value) {
                            $value = (int)$value;
                        }
                        break;
                        
                    case 'float':
                        if (is_numeric($value)) {
                            $value = (float)$value;
                        }
                        break;
                        
                    case 'boolean':
                        $lowerValue = strtolower($value);
                        if (in_array($lowerValue, ['true', '1', 'yes', 'y'])) {
                            $value = true;
                        } elseif (in_array($lowerValue, ['false', '0', 'no', 'n'])) {
                            $value = false;
                        }
                        break;
                        
                    case 'date':
                        $timestamp = strtotime($value);
                        if ($timestamp !== false) {
                            $value = date('Y-m-d', $timestamp);
                        }
                        break;
                }
            }
        }
        
        return $csvData;
    }
    
    /**
     * Infer column data type
     */
    private function inferColumnType(array $values): string
    {
        $nonEmptyValues = array_filter($values, function($value) {
            return $value !== '' && $value !== null;
        });
        
        if (empty($nonEmptyValues)) {
            return 'string';
        }
        
        $totalValues = count($nonEmptyValues);
        $integerCount = 0;
        $floatCount = 0;
        $booleanCount = 0;
        $dateCount = 0;
        
        foreach ($nonEmptyValues as $value) {
            // Check integer
            if (is_numeric($value) && (int)$value == $value) {
                $integerCount++;
            }
            // Check float
            elseif (is_numeric($value)) {
                $floatCount++;
            }
            // Check boolean
            elseif (in_array(strtolower($value), ['true', 'false', '1', '0', 'yes', 'no', 'y', 'n'])) {
                $booleanCount++;
            }
            // Check date
            elseif (strtotime($value) !== false) {
                $dateCount++;
            }
        }
        
        // Determine type based on majority (80% threshold)
        $threshold = $totalValues * 0.8;
        
        if ($integerCount >= $threshold) {
            return 'integer';
        } elseif ($floatCount + $integerCount >= $threshold) {
            return 'float';
        } elseif ($booleanCount >= $threshold) {
            return 'boolean';
        } elseif ($dateCount >= $threshold) {
            return 'date';
        }
        
        return 'string';
    }
    
    /**
     * Generate parsing statistics
     */
    private function generateParseStats(array $csvData): void
    {
        $this->parseStats = [
            'total_rows' => count($csvData),
            'total_columns' => !empty($csvData) ? count($csvData[0]) : 0,
            'error_count' => count($this->parseErrors),
            'warning_count' => count($this->parseWarnings),
            'encoding_used' => $this->config['encoding'],
            'delimiter_used' => $this->config['delimiter']
        ];
        
        if (!empty($csvData)) {
            $this->parseStats['column_names'] = array_keys($csvData[0]);
            $this->parseStats['sample_row'] = $csvData[0];
        }
    }
    
    /**
     * Helper methods
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
    
    private function resetParser(): void
    {
        $this->parseErrors = [];
        $this->parseWarnings = [];
        $this->parseStats = [];
    }
    
    private function buildParseResult(array $data, bool $success = null, string $message = null): array
    {
        if ($success === null) {
            $success = empty($this->parseErrors);
        }
        
        return [
            'success' => $success,
            'message' => $message ?: ($success ? 'CSV parsed successfully' : 'CSV parsing failed'),
            'data' => $data,
            'errors' => $this->parseErrors,
            'warnings' => $this->parseWarnings,
            'statistics' => $this->parseStats
        ];
    }
    
    /**
     * Get parser configuration
     */
    public function getConfig(): array
    {
        return $this->config;
    }
    
    /**
     * Update parser configuration
     */
    public function setConfig(array $config): void
    {
        $this->config = array_merge($this->config, $config);
    }
}
