<?php

namespace SIU\MBGL\Core;

use SIU\MBGL\Models\ValidationRule;
use SIU\MBGL\Models\SubComponent;

/**
 * Validation Rule Builder
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Programmatic builder for creating complex validation rules
 * 
 * <AUTHOR> Dharmendra Pandey
 * @version 1.0
 */
class ValidationRuleBuilder
{
    private ValidationRule $validationRuleModel;
    private SubComponent $subComponentModel;
    private array $ruleDefinitions = [];
    
    public function __construct()
    {
        $this->validationRuleModel = new ValidationRule();
        $this->subComponentModel = new SubComponent();
    }
    
    /**
     * Create validation rules for a sub-component
     */
    public function createRulesForSubComponent(int $subComponentId, array $ruleDefinitions): array
    {
        $this->ruleDefinitions = $ruleDefinitions;
        $createdRules = [];
        
        foreach ($ruleDefinitions as $definition) {
            $rule = $this->buildRule($subComponentId, $definition);
            $ruleId = $this->validationRuleModel->create($rule);
            $createdRules[] = $ruleId;
        }
        
        return $createdRules;
    }
    
    /**
     * Build a single validation rule
     */
    private function buildRule(int $subComponentId, array $definition): array
    {
        $rule = [
            'sub_component_id' => $subComponentId,
            'column_name' => $definition['column_name'],
            'display_name' => $definition['display_name'] ?? $definition['column_name'],
            'data_type' => $definition['data_type'] ?? 'string',
            'is_required' => $definition['is_required'] ?? $definition['required'] ?? false,
            'validation_type' => $definition['validation_type'],
            'validation_params' => $this->buildValidationParams($definition),
            'reference_table' => $definition['reference_table'] ?? null,
            'error_message' => $definition['error_message'] ?? $this->generateDefaultErrorMessage($definition),
            'sort_order' => $definition['sort_order'] ?? $this->getNextSortOrder($subComponentId),
            'status' => 'active'
        ];
        
        return $rule;
    }
    
    /**
     * Build validation parameters based on validation type
     */
    private function buildValidationParams(array $definition): ?string
    {
        switch ($definition['validation_type']) {
            case 'regex':
                return $definition['pattern'] ?? null;
                
            case 'range':
                return $this->buildRangeParams($definition);
                
            case 'exact_match':
                return $this->buildExactMatchParams($definition);
                
            case 'email':
                return $this->buildEmailParams($definition);
                
            case 'date':
                return $definition['date_format'] ?? 'Y-m-d';
                
            case 'phone':
                return $definition['phone_format'] ?? 'indian';
                
            case 'custom':
                return $definition['custom_rule'] ?? null;
                
            default:
                return $definition['params'] ?? null;
        }
    }
    
    /**
     * Build range validation parameters
     */
    private function buildRangeParams(array $definition): string
    {
        $params = [];
        
        if (isset($definition['min'])) {
            $params[] = "min:{$definition['min']}";
        }
        
        if (isset($definition['max'])) {
            $params[] = "max:{$definition['max']}";
        }
        
        if (isset($definition['range'])) {
            return $definition['range']; // e.g., "1-100"
        }
        
        return implode(',', $params);
    }
    
    /**
     * Build exact match parameters
     */
    private function buildExactMatchParams(array $definition): string
    {
        if (isset($definition['allowed_values'])) {
            return implode(',', $definition['allowed_values']);
        }
        
        return $definition['values'] ?? '';
    }
    
    /**
     * Build email validation parameters
     */
    private function buildEmailParams(array $definition): ?string
    {
        if (isset($definition['allowed_domains'])) {
            return implode(',', $definition['allowed_domains']);
        }
        
        return null;
    }
    
    /**
     * Generate default error message
     */
    private function generateDefaultErrorMessage(array $definition): string
    {
        $displayName = $definition['display_name'] ?? $definition['column_name'];
        
        switch ($definition['validation_type']) {
            case 'regex':
                return "Invalid format for {$displayName}";
                
            case 'range':
                if (isset($definition['min']) && isset($definition['max'])) {
                    return "{$displayName} must be between {$definition['min']} and {$definition['max']}";
                } elseif (isset($definition['min'])) {
                    return "{$displayName} must be at least {$definition['min']}";
                } elseif (isset($definition['max'])) {
                    return "{$displayName} must not exceed {$definition['max']}";
                }
                return "Invalid range for {$displayName}";
                
            case 'exact_match':
                if (isset($definition['allowed_values'])) {
                    return "{$displayName} must be one of: " . implode(', ', $definition['allowed_values']);
                }
                return "Invalid value for {$displayName}";
                
            case 'email':
                return "Invalid email format for {$displayName}";
                
            case 'date':
                return "Invalid date format for {$displayName}";
                
            case 'phone':
                return "Invalid phone number format for {$displayName}";
                
            case 'reference':
                return "Invalid reference value for {$displayName}";
                
            default:
                return "Invalid value for {$displayName}";
        }
    }
    
    /**
     * Get next sort order for sub-component
     */
    private function getNextSortOrder(int $subComponentId): int
    {
        return $this->validationRuleModel->getNextSortOrder($subComponentId);
    }
    
    /**
     * Predefined rule templates
     */
    public static function getCommonRuleTemplates(): array
    {
        return [
            'institute_code' => [
                'column_name' => 'institute_code',
                'display_name' => 'Institute Code',
                'data_type' => 'string',
                'required' => true,
                'validation_type' => 'reference',
                'reference_table' => 'institutes',
                'error_message' => 'Invalid institute code'
            ],
            
            'academic_year' => [
                'column_name' => 'academic_year',
                'display_name' => 'Academic Year',
                'data_type' => 'string',
                'required' => true,
                'validation_type' => 'academic_year',
                'error_message' => 'Invalid academic year format (expected: YYYY-YY)'
            ],
            
            'programme_code' => [
                'column_name' => 'programme_code',
                'display_name' => 'Programme Code',
                'data_type' => 'string',
                'required' => true,
                'validation_type' => 'programme_codes',
                'error_message' => 'Invalid programme code'
            ],
            
            'student_count' => [
                'column_name' => 'student_count',
                'display_name' => 'Student Count',
                'data_type' => 'integer',
                'required' => true,
                'validation_type' => 'range',
                'min' => 0,
                'max' => 10000,
                'error_message' => 'Student count must be between 0 and 10,000'
            ],
            
            'percentage' => [
                'column_name' => 'percentage',
                'display_name' => 'Percentage',
                'data_type' => 'float',
                'required' => false,
                'validation_type' => 'percentage',
                'error_message' => 'Percentage must be between 0 and 100'
            ],
            
            'email' => [
                'column_name' => 'email',
                'display_name' => 'Email Address',
                'data_type' => 'string',
                'required' => false,
                'validation_type' => 'email',
                'allowed_domains' => ['siu.edu.in', 'sibmpune.edu.in', 'siib.ac.in'],
                'error_message' => 'Invalid email address or domain not allowed'
            ],
            
            'phone' => [
                'column_name' => 'phone',
                'display_name' => 'Phone Number',
                'data_type' => 'string',
                'required' => false,
                'validation_type' => 'phone',
                'error_message' => 'Invalid Indian phone number format'
            ],
            
            'date' => [
                'column_name' => 'date',
                'display_name' => 'Date',
                'data_type' => 'date',
                'required' => false,
                'validation_type' => 'date',
                'date_format' => 'Y-m-d',
                'error_message' => 'Invalid date format (expected: YYYY-MM-DD)'
            ],
            
            'currency' => [
                'column_name' => 'amount',
                'display_name' => 'Amount',
                'data_type' => 'float',
                'required' => false,
                'validation_type' => 'currency',
                'min' => 0,
                'error_message' => 'Invalid currency amount'
            ],
            
            'sdg_indicators' => [
                'column_name' => 'sdg_indicators',
                'display_name' => 'SDG Indicators',
                'data_type' => 'string',
                'required' => false,
                'validation_type' => 'sdg_indicators',
                'error_message' => 'Invalid SDG indicator(s)'
            ],
            
            'gender' => [
                'column_name' => 'gender',
                'display_name' => 'Gender',
                'data_type' => 'string',
                'required' => false,
                'validation_type' => 'exact_match',
                'allowed_values' => ['Male', 'Female', 'Other'],
                'error_message' => 'Gender must be Male, Female, or Other'
            ],
            
            'yes_no' => [
                'column_name' => 'is_active',
                'display_name' => 'Active Status',
                'data_type' => 'boolean',
                'required' => false,
                'validation_type' => 'exact_match',
                'allowed_values' => ['Yes', 'No', '1', '0'],
                'error_message' => 'Value must be Yes/No or 1/0'
            ]
        ];
    }
    
    /**
     * Create rules from template
     */
    public function createFromTemplate(int $subComponentId, string $templateName, array $overrides = []): int
    {
        $templates = self::getCommonRuleTemplates();
        
        if (!isset($templates[$templateName])) {
            throw new \Exception("Template '{$templateName}' not found");
        }
        
        $definition = array_merge($templates[$templateName], $overrides);
        $rule = $this->buildRule($subComponentId, $definition);
        
        return $this->validationRuleModel->create($rule);
    }
    
    /**
     * Create multiple rules from templates
     */
    public function createMultipleFromTemplates(int $subComponentId, array $templateConfigs): array
    {
        $createdRules = [];
        
        foreach ($templateConfigs as $config) {
            $templateName = $config['template'];
            $overrides = $config['overrides'] ?? [];
            
            $ruleId = $this->createFromTemplate($subComponentId, $templateName, $overrides);
            $createdRules[] = $ruleId;
        }
        
        return $createdRules;
    }
    
    /**
     * Generate validation rules for common MBGL scenarios
     */
    public function generateMBGLRules(int $subComponentId, string $attributeCode): array
    {
        $ruleConfigs = $this->getMBGLRuleConfigs($attributeCode);
        $createdRules = [];
        
        foreach ($ruleConfigs as $config) {
            $rule = $this->buildRule($subComponentId, $config);
            $ruleId = $this->validationRuleModel->create($rule);
            $createdRules[] = $ruleId;
        }
        
        return $createdRules;
    }
    
    /**
     * Get MBGL-specific rule configurations
     */
    private function getMBGLRuleConfigs(string $attributeCode): array
    {
        $commonRules = [
            [
                'column_name' => 'institute_code',
                'display_name' => 'Institute Code',
                'data_type' => 'string',
                'required' => true,
                'validation_type' => 'reference',
                'reference_table' => 'institutes'
            ],
            [
                'column_name' => 'academic_year',
                'display_name' => 'Academic Year',
                'data_type' => 'string',
                'required' => true,
                'validation_type' => 'academic_year'
            ]
        ];
        
        switch ($attributeCode) {
            case 'A1': // Vision, Mission and Goals
                return array_merge($commonRules, [
                    [
                        'column_name' => 'vision_statement',
                        'display_name' => 'Vision Statement',
                        'data_type' => 'string',
                        'required' => true,
                        'validation_type' => 'regex',
                        'pattern' => '.{10,}', // At least 10 characters
                        'error_message' => 'Vision statement must be at least 10 characters long'
                    ],
                    [
                        'column_name' => 'mission_statement',
                        'display_name' => 'Mission Statement',
                        'data_type' => 'string',
                        'required' => true,
                        'validation_type' => 'regex',
                        'pattern' => '.{10,}',
                        'error_message' => 'Mission statement must be at least 10 characters long'
                    ]
                ]);
                
            case 'A3': // Academic Programs and Curriculum
                return array_merge($commonRules, [
                    [
                        'column_name' => 'programme_code',
                        'display_name' => 'Programme Code',
                        'data_type' => 'string',
                        'required' => true,
                        'validation_type' => 'programme_codes'
                    ],
                    [
                        'column_name' => 'total_students',
                        'display_name' => 'Total Students',
                        'data_type' => 'integer',
                        'required' => true,
                        'validation_type' => 'range',
                        'params' => 'min:0,max:10000'
                    ]
                ]);
                
            case 'A5': // Faculty and Staff
                return array_merge($commonRules, [
                    [
                        'column_name' => 'faculty_name',
                        'display_name' => 'Faculty Name',
                        'data_type' => 'string',
                        'required' => true,
                        'validation_type' => 'regex',
                        'pattern' => '^[a-zA-Z\s\.]+$',
                        'error_message' => 'Faculty name should contain only letters, spaces, and dots'
                    ],
                    [
                        'column_name' => 'qualification',
                        'display_name' => 'Qualification',
                        'data_type' => 'string',
                        'required' => true,
                        'validation_type' => 'exact_match',
                        'params' => 'PhD,M.Tech,M.Phil,MBA,MA,MSc,Other'
                    ]
                ]);
                
            case 'A8': // Student Support and Progression
                return array_merge($commonRules, [
                    [
                        'column_name' => 'placement_percentage',
                        'display_name' => 'Placement Percentage',
                        'data_type' => 'float',
                        'required' => false,
                        'validation_type' => 'percentage'
                    ],
                    [
                        'column_name' => 'higher_studies_percentage',
                        'display_name' => 'Higher Studies Percentage',
                        'data_type' => 'float',
                        'required' => false,
                        'validation_type' => 'percentage'
                    ]
                ]);
                
            case 'A9': // Institutional Values and Social Responsibility
                return array_merge($commonRules, [
                    [
                        'column_name' => 'sdg_indicators',
                        'display_name' => 'SDG Indicators',
                        'data_type' => 'string',
                        'required' => false,
                        'validation_type' => 'sdg_indicators'
                    ],
                    [
                        'column_name' => 'community_engagement',
                        'display_name' => 'Community Engagement',
                        'data_type' => 'string',
                        'required' => false,
                        'validation_type' => 'exact_match',
                        'params' => 'Yes,No'
                    ]
                ]);
                
            default:
                return $commonRules;
        }
    }
    
    /**
     * Validate rule definition
     */
    public function validateRuleDefinition(array $definition): array
    {
        $errors = [];
        
        // Required fields
        $requiredFields = ['column_name', 'validation_type'];
        foreach ($requiredFields as $field) {
            if (empty($definition[$field])) {
                $errors[] = "Field '{$field}' is required";
            }
        }
        
        // Validate validation type
        $validTypes = ['regex', 'reference', 'range', 'exact_match', 'email', 'date', 'url', 
                      'phone', 'academic_year', 'percentage', 'currency', 'sdg_indicators', 
                      'programme_codes', 'custom'];
        
        if (!empty($definition['validation_type']) && !in_array($definition['validation_type'], $validTypes)) {
            $errors[] = "Invalid validation type: {$definition['validation_type']}";
        }
        
        // Validate data type
        $validDataTypes = ['string', 'integer', 'float', 'boolean', 'date', 'text'];
        if (!empty($definition['data_type']) && !in_array($definition['data_type'], $validDataTypes)) {
            $errors[] = "Invalid data type: {$definition['data_type']}";
        }
        
        return $errors;
    }
}
