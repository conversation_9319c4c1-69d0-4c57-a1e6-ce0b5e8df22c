<?php

namespace SIU\MBGL\Core;

/**
 * Base Controller Class
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */
abstract class Controller
{
    protected Database $db;
    protected Session $session;
    protected Security $security;
    protected array $config;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->session = Session::getInstance();
        $this->security = new Security();

        $config = require_once __DIR__ . '/../../config/app.php';
        if (!is_array($config)) {
            $this->config = [];
        } else {
            $this->config = $config;
        }

        // Set security headers
        $this->setSecurityHeaders();
    }
    
    protected function setSecurityHeaders(): void
    {
        $securityConfig = require_once __DIR__ . '/../../config/security.php';

        if (is_array($securityConfig) && isset($securityConfig['headers'])) {
            foreach ($securityConfig['headers'] as $header => $value) {
                header("{$header}: {$value}");
            }
        }
    }
    
    protected function view(string $view, array $data = []): void
    {
        $viewPath = __DIR__ . "/../Views/{$view}.php";
        
        if (!file_exists($viewPath)) {
            throw new \Exception("View not found: {$view}");
        }
        
        // Extract data to variables
        extract($data);
        
        // Add global variables
        $config = $this->config;
        $session = $this->session;
        $user = $this->session->get('user');
        $csrf_token = $this->security->generateCSRFToken();
        
        require_once $viewPath;
    }
    
    protected function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    protected function redirect(string $url): void
    {
        header("Location: {$url}");
        exit;
    }
    
    protected function redirectWithMessage(string $url, string $message, string $type = 'info'): void
    {
        $this->session->setFlash($type, $message);
        $this->redirect($url);
    }
    
    protected function validateCSRF(): bool
    {
        return $this->security->validateCSRFToken($_POST['_token'] ?? '');
    }
    
    protected function requireAuth(): void
    {
        if (!$this->session->get('user')) {
            $this->redirect('/login');
        }
    }
    
    protected function requireRole(string $role): void
    {
        $this->requireAuth();
        
        $user = $this->session->get('user');
        if (!$user || $user['role'] !== $role) {
            $this->json(['error' => 'Insufficient permissions'], 403);
        }
    }
    
    protected function requireRoles(array $roles): void
    {
        $this->requireAuth();
        
        $user = $this->session->get('user');
        if (!$user || !in_array($user['role'], $roles)) {
            $this->json(['error' => 'Insufficient permissions'], 403);
        }
    }
    
    protected function validateInput(array $rules, array $data): array
    {
        $validator = new Validator();
        return $validator->validate($data, $rules);
    }
    
    protected function sanitizeInput(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    protected function logActivity(string $action, array $details = []): void
    {
        $user = $this->session->get('user');
        $logData = [
            'user_id' => $user['id'] ?? null,
            'action' => $action,
            'details' => json_encode($details),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            $this->db->insert('activity_logs', $logData);
        } catch (\Exception $e) {
            // Log to file if database logging fails
            error_log("Activity log failed: " . $e->getMessage());
        }
    }
    
    protected function getPaginationData(int $page, int $perPage, int $total): array
    {
        $totalPages = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;
        
        return [
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'total_pages' => $totalPages,
            'offset' => $offset,
            'has_previous' => $page > 1,
            'has_next' => $page < $totalPages,
            'previous_page' => $page > 1 ? $page - 1 : null,
            'next_page' => $page < $totalPages ? $page + 1 : null
        ];
    }
}
