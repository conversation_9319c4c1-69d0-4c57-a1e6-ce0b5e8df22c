-- Migration: Update validation_rules table to support extended validation types
-- MBGL AQAR Management System - Symbiosis International University
-- Author: Dr. <PERSON><PERSON><PERSON>
-- Version: 1.0

-- Update validation_type enum to include all new validation types
ALTER TABLE `validation_rules` 
MODIFY COLUMN `validation_type` ENUM(
    'regex',
    'reference', 
    'range',
    'exact_match',
    'not_empty',
    'binary',
    'email',
    'date',
    'url',
    'phone',
    'academic_year',
    'percentage',
    'currency',
    'sdg_indicators',
    'programme_codes',
    'custom'
) NOT NULL;

-- Add index on validation_type for better query performance
ALTER TABLE `validation_rules` ADD INDEX `idx_validation_type` (`validation_type`);

-- Add index on status for better query performance
ALTER TABLE `validation_rules` ADD INDEX `idx_status` (`status`);

-- Update validation_params to LONGTEXT for complex validation rules
ALTER TABLE `validation_rules` 
MODIFY COLUMN `validation_params` LONGTEXT;

-- Update error_message to TEXT for longer error messages
ALTER TABLE `validation_rules` 
MODIFY COLUMN `error_message` TEXT NOT NULL;
