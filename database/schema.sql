-- MBGL AQAR Management System - Database Schema
-- Symbiosis International University
-- Author: Dr. <PERSON><PERSON><PERSON><PERSON>
-- Version: 1.0

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- Database: mbgl_aqar_system
CREATE DATABASE IF NOT EXISTS `mbgl_aqar_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `mbgl_aqar_system`;

-- --------------------------------------------------------
-- Table structure for table `institutes`
-- --------------------------------------------------------

CREATE TABLE `institutes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `institute_code` varchar(10) NOT NULL,
  `institute_name` varchar(255) NOT NULL,
  `institute_short_name` varchar(50) NOT NULL,
  `faculty` enum('LAW','MANAGEMENT','COMPUTER STUDIES','MEDICAL AND HEALTH SCIENCES','MEDIA & COMMUNICATION','HUMANITIES & SOCIAL SCIENCES','ENGINEERING','ARCHITECTURE & DESIGN') NOT NULL,
  `city` enum('Pune','Noida','Hyderabad','Nagpur','Nashik','Bengaluru') NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `institute_code` (`institute_code`),
  UNIQUE KEY `institute_short_name` (`institute_short_name`),
  KEY `faculty` (`faculty`),
  KEY `city` (`city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `users`
-- --------------------------------------------------------

CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `institute_id` int(11) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `security_code` varchar(6) NOT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `role` enum('Super Admin','NAAC Coordinator','Quality Assurance Officer','Academic Administrator') NOT NULL,
  `status` enum('active','inactive','locked') DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `institute_id` (`institute_id`),
  KEY `role` (`role`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`institute_id`) REFERENCES `institutes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `mbgl_attributes`
-- --------------------------------------------------------

CREATE TABLE `mbgl_attributes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attribute_code` varchar(10) NOT NULL,
  `attribute_name` varchar(255) NOT NULL,
  `attribute_type` enum('INPUT','PROCESS','OUTCOME','EXTENDED') NOT NULL,
  `max_points` int(11) NOT NULL,
  `description` text,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `attribute_code` (`attribute_code`),
  KEY `attribute_type` (`attribute_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `sub_components`
-- --------------------------------------------------------

CREATE TABLE `sub_components` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attribute_id` int(11) NOT NULL,
  `component_code` varchar(20) NOT NULL,
  `component_name` varchar(255) NOT NULL,
  `component_description` text,
  `max_points` decimal(6,2) NOT NULL,
  `data_type` enum('quantitative','qualitative') NOT NULL,
  `applicable_to_schools` tinyint(1) DEFAULT 1,
  `applicable_to_colleges` tinyint(1) DEFAULT 1,
  `applicable_to_faculties` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `component_code` (`component_code`),
  KEY `attribute_id` (`attribute_id`),
  CONSTRAINT `sub_components_ibfk_1` FOREIGN KEY (`attribute_id`) REFERENCES `mbgl_attributes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `validation_rules`
-- --------------------------------------------------------

CREATE TABLE `validation_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_component_id` int(11) NOT NULL,
  `column_name` varchar(100) NOT NULL,
  `display_name` varchar(255) NOT NULL,
  `data_type` varchar(50) NOT NULL,
  `is_required` tinyint(1) DEFAULT 0,
  `validation_type` enum('regex','reference','range','exact_match','not_empty','binary') NOT NULL,
  `validation_params` text,
  `reference_table` varchar(100) DEFAULT NULL,
  `error_message` varchar(255) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `sub_component_id` (`sub_component_id`),
  KEY `column_name` (`column_name`),
  CONSTRAINT `validation_rules_ibfk_1` FOREIGN KEY (`sub_component_id`) REFERENCES `sub_components` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `sdg_indicators`
-- --------------------------------------------------------

CREATE TABLE `sdg_indicators` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `indicator_name` varchar(100) NOT NULL,
  `indicator_description` text,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `indicator_name` (`indicator_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `programme_codes`
-- --------------------------------------------------------

CREATE TABLE `programme_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `institute_id` int(11) NOT NULL,
  `programme_code` varchar(5) NOT NULL,
  `programme_name` varchar(255) NOT NULL,
  `programme_type` varchar(50) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `programme_code` (`programme_code`),
  KEY `institute_id` (`institute_id`),
  CONSTRAINT `programme_codes_ibfk_1` FOREIGN KEY (`institute_id`) REFERENCES `institutes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `data_submissions`
-- --------------------------------------------------------

CREATE TABLE `data_submissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `institute_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `sub_component_id` int(11) NOT NULL,
  `academic_year` varchar(9) NOT NULL,
  `submission_type` enum('csv','manual') NOT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `total_records` int(11) DEFAULT 0,
  `valid_records` int(11) DEFAULT 0,
  `invalid_records` int(11) DEFAULT 0,
  `validation_status` enum('pending','processing','completed','failed') DEFAULT 'pending',
  `validation_errors` longtext,
  `submission_status` enum('draft','submitted','approved','rejected') DEFAULT 'draft',
  `submitted_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `institute_id` (`institute_id`),
  KEY `user_id` (`user_id`),
  KEY `sub_component_id` (`sub_component_id`),
  KEY `academic_year` (`academic_year`),
  KEY `approved_by` (`approved_by`),
  CONSTRAINT `data_submissions_ibfk_1` FOREIGN KEY (`institute_id`) REFERENCES `institutes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `data_submissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `data_submissions_ibfk_3` FOREIGN KEY (`sub_component_id`) REFERENCES `sub_components` (`id`) ON DELETE CASCADE,
  CONSTRAINT `data_submissions_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `submission_data`
-- --------------------------------------------------------

CREATE TABLE `submission_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `submission_id` int(11) NOT NULL,
  `row_number` int(11) NOT NULL,
  `data_json` longtext NOT NULL,
  `validation_status` enum('valid','invalid') DEFAULT 'valid',
  `validation_errors` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `submission_id` (`submission_id`),
  KEY `row_number` (`row_number`),
  CONSTRAINT `submission_data_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `data_submissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `scores`
-- --------------------------------------------------------

CREATE TABLE `scores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `institute_id` int(11) NOT NULL,
  `sub_component_id` int(11) NOT NULL,
  `academic_year` varchar(9) NOT NULL,
  `calculated_score` decimal(6,2) DEFAULT 0.00,
  `max_possible_score` decimal(6,2) NOT NULL,
  `score_percentage` decimal(5,2) DEFAULT 0.00,
  `calculation_method` varchar(100) DEFAULT NULL,
  `calculation_details` text,
  `last_calculated` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `calculated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_score` (`institute_id`,`sub_component_id`,`academic_year`),
  KEY `calculated_by` (`calculated_by`),
  CONSTRAINT `scores_ibfk_1` FOREIGN KEY (`institute_id`) REFERENCES `institutes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `scores_ibfk_2` FOREIGN KEY (`sub_component_id`) REFERENCES `sub_components` (`id`) ON DELETE CASCADE,
  CONSTRAINT `scores_ibfk_3` FOREIGN KEY (`calculated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `attribute_scores`
-- --------------------------------------------------------

CREATE TABLE `attribute_scores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `institute_id` int(11) NOT NULL,
  `attribute_id` int(11) NOT NULL,
  `academic_year` varchar(9) NOT NULL,
  `total_score` decimal(6,2) DEFAULT 0.00,
  `max_possible_score` decimal(6,2) NOT NULL,
  `score_percentage` decimal(5,2) DEFAULT 0.00,
  `last_calculated` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `calculated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_attribute_score` (`institute_id`,`attribute_id`,`academic_year`),
  KEY `calculated_by` (`calculated_by`),
  CONSTRAINT `attribute_scores_ibfk_1` FOREIGN KEY (`institute_id`) REFERENCES `institutes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `attribute_scores_ibfk_2` FOREIGN KEY (`attribute_id`) REFERENCES `mbgl_attributes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `attribute_scores_ibfk_3` FOREIGN KEY (`calculated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `overall_scores`
-- --------------------------------------------------------

CREATE TABLE `overall_scores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `institute_id` int(11) NOT NULL,
  `academic_year` varchar(9) NOT NULL,
  `input_score` decimal(6,2) DEFAULT 0.00,
  `process_score` decimal(6,2) DEFAULT 0.00,
  `outcome_score` decimal(6,2) DEFAULT 0.00,
  `extended_score` decimal(6,2) DEFAULT 0.00,
  `total_score` decimal(6,2) DEFAULT 0.00,
  `max_possible_score` decimal(6,2) DEFAULT 1000.00,
  `score_percentage` decimal(5,2) DEFAULT 0.00,
  `grade` varchar(10) DEFAULT NULL,
  `last_calculated` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `calculated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_overall_score` (`institute_id`,`academic_year`),
  KEY `calculated_by` (`calculated_by`),
  CONSTRAINT `overall_scores_ibfk_1` FOREIGN KEY (`institute_id`) REFERENCES `institutes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `overall_scores_ibfk_2` FOREIGN KEY (`calculated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `evidence_files`
-- --------------------------------------------------------

CREATE TABLE `evidence_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `submission_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `uploaded_by` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `submission_id` (`submission_id`),
  KEY `uploaded_by` (`uploaded_by`),
  CONSTRAINT `evidence_files_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `data_submissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `evidence_files_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `activity_logs`
-- --------------------------------------------------------

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `details` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `action` (`action`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `reports`
-- --------------------------------------------------------

CREATE TABLE `reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `institute_id` int(11) DEFAULT NULL,
  `report_type` enum('institute','faculty','overall','comparative') NOT NULL,
  `report_title` varchar(255) NOT NULL,
  `academic_year` varchar(9) NOT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `generated_by` int(11) NOT NULL,
  `generated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `parameters` text,
  `status` enum('generating','completed','failed') DEFAULT 'generating',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `institute_id` (`institute_id`),
  KEY `generated_by` (`generated_by`),
  KEY `report_type` (`report_type`),
  CONSTRAINT `reports_ibfk_1` FOREIGN KEY (`institute_id`) REFERENCES `institutes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `reports_ibfk_2` FOREIGN KEY (`generated_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `system_settings`
-- --------------------------------------------------------

CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('string','integer','boolean','json') DEFAULT 'string',
  `description` text,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `system_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `migrations`
-- --------------------------------------------------------

CREATE TABLE `migrations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  `executed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Enable foreign key checks
-- --------------------------------------------------------

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;
