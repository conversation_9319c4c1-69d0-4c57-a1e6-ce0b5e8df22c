<?php

/**
 * Validation Rules Seed Data
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Seeds comprehensive validation rules for all sub-components
 * 
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version 1.0
 */

require_once __DIR__ . '/../../vendor/autoload.php';

// Load environment variables
$envFile = __DIR__ . '/../../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

use SIU\MBGL\Core\Database;
use SIU\MBGL\Models\SubComponent;
use SIU\MBGL\Core\ValidationRuleBuilder;

try {
    $db = Database::getInstance();
    $subComponentModel = new SubComponent();
    $ruleBuilder = new ValidationRuleBuilder();
    
    echo "Starting Validation Rules seed...\n";
    
    // Get all sub-components
    $subComponents = $subComponentModel->getSubComponentsWithValidationRules();
    
    if (empty($subComponents)) {
        echo "No sub-components found. Please run the MBGL attributes seed first.\n";
        exit(1);
    }
    
    $totalRulesCreated = 0;
    
    foreach ($subComponents as $subComponent) {
        echo "Creating validation rules for: {$subComponent['attribute_code']}.{$subComponent['component_code']}\n";
        
        // Skip if rules already exist
        if ($subComponent['validation_rule_count'] > 0) {
            echo "  Validation rules already exist, skipping...\n";
            continue;
        }
        
        // Generate rules based on attribute code
        $ruleConfigs = getValidationRulesForSubComponent($subComponent);
        
        if (empty($ruleConfigs)) {
            echo "  No validation rules defined for this sub-component\n";
            continue;
        }
        
        try {
            $createdRules = $ruleBuilder->createRulesForSubComponent($subComponent['id'], $ruleConfigs);
            $totalRulesCreated += count($createdRules);
            echo "  Created " . count($createdRules) . " validation rules\n";
        } catch (Exception $e) {
            echo "  Error creating rules: " . $e->getMessage() . "\n";
        }
    }
    
    echo "Validation Rules seed completed successfully!\n";
    echo "Total validation rules created: {$totalRulesCreated}\n";
    
} catch (Exception $e) {
    echo "Error seeding Validation Rules: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Get validation rules configuration for a sub-component
 */
function getValidationRulesForSubComponent(array $subComponent): array
{
    $attributeCode = $subComponent['attribute_code'];
    $componentCode = $subComponent['component_code'];
    
    // Common rules for all sub-components
    $commonRules = [
        [
            'column_name' => 'institute_code',
            'display_name' => 'Institute Code',
            'data_type' => 'string',
            'is_required' => true,
            'validation_type' => 'reference',
            'reference_table' => 'institutes',
            'error_message' => 'Invalid institute code. Please use a valid SIU institute code.',
            'sort_order' => 1
        ],
        [
            'column_name' => 'academic_year',
            'display_name' => 'Academic Year',
            'data_type' => 'string',
            'is_required' => true,
            'validation_type' => 'academic_year',
            'error_message' => 'Invalid academic year format. Use YYYY-YY format (e.g., 2023-24).',
            'sort_order' => 2
        ]
    ];
    
    // Specific rules based on attribute and component
    $specificRules = [];
    
    switch ($attributeCode) {
        case 'A1': // Vision, Mission and Goals
            $specificRules = getA1ValidationRules($componentCode);
            break;
            
        case 'A2': // Governance and Leadership
            $specificRules = getA2ValidationRules($componentCode);
            break;
            
        case 'A3': // Academic Programs and Curriculum
            $specificRules = getA3ValidationRules($componentCode);
            break;
            
        case 'A4': // Teaching-Learning and Assessment
            $specificRules = getA4ValidationRules($componentCode);
            break;
            
        case 'A5': // Faculty and Staff
            $specificRules = getA5ValidationRules($componentCode);
            break;
            
        case 'A6': // Research and Innovation
            $specificRules = getA6ValidationRules($componentCode);
            break;
            
        case 'A7': // Infrastructure and Learning Resources
            $specificRules = getA7ValidationRules($componentCode);
            break;
            
        case 'A8': // Student Support and Progression
            $specificRules = getA8ValidationRules($componentCode);
            break;
            
        case 'A9': // Institutional Values and Social Responsibility
            $specificRules = getA9ValidationRules($componentCode);
            break;
            
        case 'A10': // Quality Assurance and Enhancement
            $specificRules = getA10ValidationRules($componentCode);
            break;
    }
    
    // Merge common and specific rules
    return array_merge($commonRules, $specificRules);
}

/**
 * A1: Vision, Mission and Goals validation rules
 */
function getA1ValidationRules(string $componentCode): array
{
    switch ($componentCode) {
        case 'A1.1': // Vision and Mission Statement
            return [
                [
                    'column_name' => 'vision_statement',
                    'display_name' => 'Vision Statement',
                    'data_type' => 'text',
                    'is_required' => true,
                    'validation_type' => 'regex',
                    'validation_params' => '.{20,}',
                    'error_message' => 'Vision statement must be at least 20 characters long.',
                    'sort_order' => 3
                ],
                [
                    'column_name' => 'mission_statement',
                    'display_name' => 'Mission Statement',
                    'data_type' => 'text',
                    'is_required' => true,
                    'validation_type' => 'regex',
                    'validation_params' => '.{20,}',
                    'error_message' => 'Mission statement must be at least 20 characters long.',
                    'sort_order' => 4
                ],
                [
                    'column_name' => 'approval_date',
                    'display_name' => 'Approval Date',
                    'data_type' => 'date',
                    'is_required' => false,
                    'validation_type' => 'date',
                    'validation_params' => 'Y-m-d',
                    'error_message' => 'Invalid date format. Use YYYY-MM-DD format.',
                    'sort_order' => 5
                ]
            ];
            
        case 'A1.2': // Strategic Goals and Objectives
            return [
                [
                    'column_name' => 'goal_description',
                    'display_name' => 'Goal Description',
                    'data_type' => 'text',
                    'is_required' => true,
                    'validation_type' => 'regex',
                    'validation_params' => '.{10,}',
                    'error_message' => 'Goal description must be at least 10 characters long.',
                    'sort_order' => 3
                ],
                [
                    'column_name' => 'target_completion_year',
                    'display_name' => 'Target Completion Year',
                    'data_type' => 'integer',
                    'is_required' => false,
                    'validation_type' => 'range',
                    'validation_params' => '2020-2030',
                    'error_message' => 'Target completion year must be between 2020 and 2030.',
                    'sort_order' => 4
                ]
            ];
            
        default:
            return [];
    }
}

/**
 * A3: Academic Programs and Curriculum validation rules
 */
function getA3ValidationRules(string $componentCode): array
{
    switch ($componentCode) {
        case 'A3.1': // Program Design and Structure
        case 'A3.2': // Curriculum Relevance
        case 'A3.3': // Program Outcomes
        case 'A3.4': // Curriculum Review and Update
            return [
                [
                    'column_name' => 'programme_code',
                    'display_name' => 'Programme Code',
                    'data_type' => 'string',
                    'is_required' => true,
                    'validation_type' => 'programme_codes',
                    'error_message' => 'Invalid programme code for this institute.',
                    'sort_order' => 3
                ],
                [
                    'column_name' => 'programme_name',
                    'display_name' => 'Programme Name',
                    'data_type' => 'string',
                    'is_required' => true,
                    'validation_type' => 'regex',
                    'validation_params' => '^[a-zA-Z0-9\s\-\(\)\.]+$',
                    'error_message' => 'Programme name contains invalid characters.',
                    'sort_order' => 4
                ],
                [
                    'column_name' => 'total_students',
                    'display_name' => 'Total Students',
                    'data_type' => 'integer',
                    'is_required' => true,
                    'validation_type' => 'range',
                    'validation_params' => 'min:0,max:5000',
                    'error_message' => 'Total students must be between 0 and 5000.',
                    'sort_order' => 5
                ],
                [
                    'column_name' => 'male_students',
                    'display_name' => 'Male Students',
                    'data_type' => 'integer',
                    'is_required' => false,
                    'validation_type' => 'range',
                    'validation_params' => 'min:0',
                    'error_message' => 'Male students count cannot be negative.',
                    'sort_order' => 6
                ],
                [
                    'column_name' => 'female_students',
                    'display_name' => 'Female Students',
                    'data_type' => 'integer',
                    'is_required' => false,
                    'validation_type' => 'range',
                    'validation_params' => 'min:0',
                    'error_message' => 'Female students count cannot be negative.',
                    'sort_order' => 7
                ]
            ];
            
        default:
            return [];
    }
}

/**
 * A5: Faculty and Staff validation rules
 */
function getA5ValidationRules(string $componentCode): array
{
    return [
        [
            'column_name' => 'faculty_name',
            'display_name' => 'Faculty Name',
            'data_type' => 'string',
            'is_required' => true,
            'validation_type' => 'regex',
            'validation_params' => '^[a-zA-Z\s\.]+$',
            'error_message' => 'Faculty name should contain only letters, spaces, and dots.',
            'sort_order' => 3
        ],
        [
            'column_name' => 'designation',
            'display_name' => 'Designation',
            'data_type' => 'string',
            'is_required' => true,
            'validation_type' => 'exact_match',
            'validation_params' => 'Professor,Associate Professor,Assistant Professor,Lecturer,Visiting Faculty',
            'error_message' => 'Invalid designation.',
            'sort_order' => 4
        ],
        [
            'column_name' => 'qualification',
            'display_name' => 'Qualification',
            'data_type' => 'string',
            'is_required' => true,
            'validation_type' => 'exact_match',
            'validation_params' => 'PhD,M.Tech,M.Phil,MBA,MA,MSc,MCom,LLM,Other',
            'error_message' => 'Invalid qualification.',
            'sort_order' => 5
        ],
        [
            'column_name' => 'experience_years',
            'display_name' => 'Experience (Years)',
            'data_type' => 'integer',
            'is_required' => false,
            'validation_type' => 'range',
            'validation_params' => 'min:0,max:50',
            'error_message' => 'Experience must be between 0 and 50 years.',
            'sort_order' => 6
        ],
        [
            'column_name' => 'email',
            'display_name' => 'Email Address',
            'data_type' => 'string',
            'is_required' => false,
            'validation_type' => 'email',
            'validation_params' => 'siu.edu.in,sibmpune.edu.in,siib.ac.in,scmhrd.edu,sims.edu,sidtm.edu.in',
            'error_message' => 'Invalid email address or domain not allowed.',
            'sort_order' => 7
        ]
    ];
}

/**
 * A8: Student Support and Progression validation rules
 */
function getA8ValidationRules(string $componentCode): array
{
    return [
        [
            'column_name' => 'programme_code',
            'display_name' => 'Programme Code',
            'data_type' => 'string',
            'is_required' => true,
            'validation_type' => 'programme_codes',
            'error_message' => 'Invalid programme code for this institute.',
            'sort_order' => 3
        ],
        [
            'column_name' => 'total_graduates',
            'display_name' => 'Total Graduates',
            'data_type' => 'integer',
            'is_required' => true,
            'validation_type' => 'range',
            'validation_params' => 'min:0,max:2000',
            'error_message' => 'Total graduates must be between 0 and 2000.',
            'sort_order' => 4
        ],
        [
            'column_name' => 'placed_students',
            'display_name' => 'Placed Students',
            'data_type' => 'integer',
            'is_required' => false,
            'validation_type' => 'range',
            'validation_params' => 'min:0',
            'error_message' => 'Placed students count cannot be negative.',
            'sort_order' => 5
        ],
        [
            'column_name' => 'placement_percentage',
            'display_name' => 'Placement Percentage',
            'data_type' => 'float',
            'is_required' => false,
            'validation_type' => 'percentage',
            'error_message' => 'Placement percentage must be between 0 and 100.',
            'sort_order' => 6
        ],
        [
            'column_name' => 'higher_studies',
            'display_name' => 'Students in Higher Studies',
            'data_type' => 'integer',
            'is_required' => false,
            'validation_type' => 'range',
            'validation_params' => 'min:0',
            'error_message' => 'Higher studies count cannot be negative.',
            'sort_order' => 7
        ],
        [
            'column_name' => 'average_salary',
            'display_name' => 'Average Salary (LPA)',
            'data_type' => 'float',
            'is_required' => false,
            'validation_type' => 'currency',
            'validation_params' => 'min:0,max:100',
            'error_message' => 'Average salary must be between 0 and 100 LPA.',
            'sort_order' => 8
        ]
    ];
}

/**
 * A9: Institutional Values and Social Responsibility validation rules
 */
function getA9ValidationRules(string $componentCode): array
{
    return [
        [
            'column_name' => 'activity_title',
            'display_name' => 'Activity Title',
            'data_type' => 'string',
            'is_required' => true,
            'validation_type' => 'regex',
            'validation_params' => '.{5,}',
            'error_message' => 'Activity title must be at least 5 characters long.',
            'sort_order' => 3
        ],
        [
            'column_name' => 'sdg_indicators',
            'display_name' => 'SDG Indicators',
            'data_type' => 'string',
            'is_required' => false,
            'validation_type' => 'sdg_indicators',
            'error_message' => 'Invalid SDG indicator(s). Use comma-separated valid SDG indicator names.',
            'sort_order' => 4
        ],
        [
            'column_name' => 'participants_count',
            'display_name' => 'Number of Participants',
            'data_type' => 'integer',
            'is_required' => false,
            'validation_type' => 'range',
            'validation_params' => 'min:1,max:10000',
            'error_message' => 'Participants count must be between 1 and 10000.',
            'sort_order' => 5
        ],
        [
            'column_name' => 'activity_date',
            'display_name' => 'Activity Date',
            'data_type' => 'date',
            'is_required' => false,
            'validation_type' => 'date',
            'validation_params' => 'Y-m-d',
            'error_message' => 'Invalid date format. Use YYYY-MM-DD format.',
            'sort_order' => 6
        ],
        [
            'column_name' => 'community_impact',
            'display_name' => 'Community Impact',
            'data_type' => 'string',
            'is_required' => false,
            'validation_type' => 'exact_match',
            'validation_params' => 'High,Medium,Low',
            'error_message' => 'Community impact must be High, Medium, or Low.',
            'sort_order' => 7
        ]
    ];
}

// Placeholder functions for other attributes
function getA2ValidationRules(string $componentCode): array { return []; }
function getA4ValidationRules(string $componentCode): array { return []; }
function getA6ValidationRules(string $componentCode): array { return []; }
function getA7ValidationRules(string $componentCode): array { return []; }
function getA10ValidationRules(string $componentCode): array { return []; }
