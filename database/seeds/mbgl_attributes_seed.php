<?php

/**
 * MBGL Attributes Seed Data
 * MBGL AQAR Management System - Symbiosis International University
 * 
 * Seeds the 10 quality attributes (A1-A10) of the MBGL framework
 * 
 * <AUTHOR> <PERSON><PERSON>
 * @version 1.0
 */

require_once __DIR__ . '/../../vendor/autoload.php';

// Load environment variables
$envFile = __DIR__ . '/../../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

use SIU\MBGL\Core\Database;
use SIU\MBGL\Models\MBGLAttribute;
use SIU\MBGL\Models\SubComponent;
use SIU\MBGL\Models\SDGIndicator;

try {
    $db = Database::getInstance();
    $attributeModel = new MBGLAttribute();
    $subComponentModel = new SubComponent();
    $sdgModel = new SDGIndicator();
    
    echo "Starting MBGL Framework seed...\n";
    
    // MBGL Attributes (A1-A10)
    $attributes = [
        [
            'attribute_code' => 'A1',
            'attribute_name' => 'Vision, Mission and Goals',
            'attribute_type' => 'INPUT',
            'max_points' => 100,
            'description' => 'Institutional vision, mission, goals and objectives that guide the institution in its pursuit of excellence',
            'sort_order' => 1,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A2',
            'attribute_name' => 'Governance and Leadership',
            'attribute_type' => 'INPUT',
            'max_points' => 100,
            'description' => 'Governance structure, leadership quality, and institutional management systems',
            'sort_order' => 2,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A3',
            'attribute_name' => 'Academic Programs and Curriculum',
            'attribute_type' => 'PROCESS',
            'max_points' => 150,
            'description' => 'Quality and relevance of academic programs, curriculum design and implementation',
            'sort_order' => 3,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A4',
            'attribute_name' => 'Teaching-Learning and Assessment',
            'attribute_type' => 'PROCESS',
            'max_points' => 150,
            'description' => 'Teaching-learning processes, pedagogical practices, and assessment methods',
            'sort_order' => 4,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A5',
            'attribute_name' => 'Faculty and Staff',
            'attribute_type' => 'INPUT',
            'max_points' => 100,
            'description' => 'Faculty qualifications, development, performance, and staff adequacy',
            'sort_order' => 5,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A6',
            'attribute_name' => 'Research and Innovation',
            'attribute_type' => 'PROCESS',
            'max_points' => 100,
            'description' => 'Research culture, innovation, publications, and knowledge creation',
            'sort_order' => 6,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A7',
            'attribute_name' => 'Infrastructure and Learning Resources',
            'attribute_type' => 'INPUT',
            'max_points' => 100,
            'description' => 'Physical infrastructure, learning resources, and technology support',
            'sort_order' => 7,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A8',
            'attribute_name' => 'Student Support and Progression',
            'attribute_type' => 'OUTCOME',
            'max_points' => 100,
            'description' => 'Student support services, progression, and overall development',
            'sort_order' => 8,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A9',
            'attribute_name' => 'Institutional Values and Social Responsibility',
            'attribute_type' => 'EXTENDED',
            'max_points' => 50,
            'description' => 'Institutional values, ethics, social responsibility, and community engagement',
            'sort_order' => 9,
            'status' => 'active'
        ],
        [
            'attribute_code' => 'A10',
            'attribute_name' => 'Quality Assurance and Enhancement',
            'attribute_type' => 'OUTCOME',
            'max_points' => 50,
            'description' => 'Quality assurance mechanisms, continuous improvement, and institutional effectiveness',
            'sort_order' => 10,
            'status' => 'active'
        ]
    ];
    
    echo "Seeding MBGL Attributes...\n";
    foreach ($attributes as $attribute) {
        $existingAttribute = $attributeModel->findByCode($attribute['attribute_code']);
        if (!$existingAttribute) {
            $attributeId = $attributeModel->create($attribute);
            echo "Created attribute: {$attribute['attribute_code']} - {$attribute['attribute_name']}\n";
            
            // Create sample sub-components for each attribute
            $subComponents = getSampleSubComponents($attribute['attribute_code'], $attributeId);
            foreach ($subComponents as $subComponent) {
                $subComponentModel->create($subComponent);
                echo "  Created sub-component: {$subComponent['component_code']}\n";
            }
        } else {
            echo "Attribute {$attribute['attribute_code']} already exists, skipping...\n";
        }
    }
    
    // SDG Indicators
    $sdgIndicators = [
        ['indicator_name' => 'No Poverty', 'indicator_description' => 'End poverty in all its forms everywhere', 'sort_order' => 1],
        ['indicator_name' => 'Zero Hunger', 'indicator_description' => 'End hunger, achieve food security and improved nutrition', 'sort_order' => 2],
        ['indicator_name' => 'Good Health and Well-being', 'indicator_description' => 'Ensure healthy lives and promote well-being for all', 'sort_order' => 3],
        ['indicator_name' => 'Quality Education', 'indicator_description' => 'Ensure inclusive and equitable quality education', 'sort_order' => 4],
        ['indicator_name' => 'Gender Equality', 'indicator_description' => 'Achieve gender equality and empower all women and girls', 'sort_order' => 5],
        ['indicator_name' => 'Clean Water and Sanitation', 'indicator_description' => 'Ensure availability and sustainable management of water', 'sort_order' => 6],
        ['indicator_name' => 'Affordable and Clean Energy', 'indicator_description' => 'Ensure access to affordable, reliable, sustainable energy', 'sort_order' => 7],
        ['indicator_name' => 'Decent Work and Economic Growth', 'indicator_description' => 'Promote sustained, inclusive economic growth', 'sort_order' => 8],
        ['indicator_name' => 'Industry, Innovation and Infrastructure', 'indicator_description' => 'Build resilient infrastructure, promote innovation', 'sort_order' => 9],
        ['indicator_name' => 'Reduced Inequalities', 'indicator_description' => 'Reduce inequality within and among countries', 'sort_order' => 10],
        ['indicator_name' => 'Sustainable Cities and Communities', 'indicator_description' => 'Make cities and human settlements inclusive and sustainable', 'sort_order' => 11],
        ['indicator_name' => 'Responsible Consumption and Production', 'indicator_description' => 'Ensure sustainable consumption and production patterns', 'sort_order' => 12],
        ['indicator_name' => 'Climate Action', 'indicator_description' => 'Take urgent action to combat climate change', 'sort_order' => 13],
        ['indicator_name' => 'Life Below Water', 'indicator_description' => 'Conserve and sustainably use the oceans, seas and marine resources', 'sort_order' => 14],
        ['indicator_name' => 'Life on Land', 'indicator_description' => 'Protect, restore and promote sustainable use of terrestrial ecosystems', 'sort_order' => 15],
        ['indicator_name' => 'Peace, Justice and Strong Institutions', 'indicator_description' => 'Promote peaceful and inclusive societies for sustainable development', 'sort_order' => 16],
        ['indicator_name' => 'Partnerships for the Goals', 'indicator_description' => 'Strengthen the means of implementation and revitalize partnerships', 'sort_order' => 17]
    ];
    
    echo "Seeding SDG Indicators...\n";
    foreach ($sdgIndicators as $indicator) {
        $indicator['status'] = 'active';
        $existing = $sdgModel->findByName($indicator['indicator_name']);
        if (!$existing) {
            $sdgModel->create($indicator);
            echo "Created SDG indicator: {$indicator['indicator_name']}\n";
        } else {
            echo "SDG indicator {$indicator['indicator_name']} already exists, skipping...\n";
        }
    }
    
    echo "MBGL Framework seed completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error seeding MBGL Framework: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Get sample sub-components for each attribute
 */
function getSampleSubComponents(string $attributeCode, int $attributeId): array
{
    $subComponents = [];
    
    switch ($attributeCode) {
        case 'A1':
            $subComponents = [
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A1.1',
                    'component_name' => 'Vision and Mission Statement',
                    'component_description' => 'Clear, well-defined vision and mission statements',
                    'max_points' => 25.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 1,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A1.2',
                    'component_name' => 'Strategic Goals and Objectives',
                    'component_description' => 'Well-defined strategic goals and measurable objectives',
                    'max_points' => 25.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 2,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A1.3',
                    'component_name' => 'Stakeholder Alignment',
                    'component_description' => 'Alignment of vision-mission with stakeholder expectations',
                    'max_points' => 25.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 3,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A1.4',
                    'component_name' => 'Implementation and Review',
                    'component_description' => 'Implementation mechanisms and periodic review processes',
                    'max_points' => 25.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 4,
                    'status' => 'active'
                ]
            ];
            break;
            
        case 'A2':
            $subComponents = [
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A2.1',
                    'component_name' => 'Governance Structure',
                    'component_description' => 'Effective governance structure and decision-making processes',
                    'max_points' => 30.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 1,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A2.2',
                    'component_name' => 'Leadership Quality',
                    'component_description' => 'Quality of leadership at various levels',
                    'max_points' => 35.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 2,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A2.3',
                    'component_name' => 'Management Systems',
                    'component_description' => 'Institutional management and administrative systems',
                    'max_points' => 35.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 3,
                    'status' => 'active'
                ]
            ];
            break;
            
        case 'A3':
            $subComponents = [
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A3.1',
                    'component_name' => 'Program Design and Structure',
                    'component_description' => 'Quality of academic program design and structure',
                    'max_points' => 40.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 1,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A3.2',
                    'component_name' => 'Curriculum Relevance',
                    'component_description' => 'Relevance and currency of curriculum content',
                    'max_points' => 40.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 2,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A3.3',
                    'component_name' => 'Program Outcomes',
                    'component_description' => 'Clear definition and achievement of program outcomes',
                    'max_points' => 35.0,
                    'data_type' => 'quantitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 3,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => 'A3.4',
                    'component_name' => 'Curriculum Review and Update',
                    'component_description' => 'Regular review and updating of curriculum',
                    'max_points' => 35.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 4,
                    'status' => 'active'
                ]
            ];
            break;
            
        // Add more sub-components for other attributes as needed
        default:
            // Generic sub-components for other attributes
            $subComponents = [
                [
                    'attribute_id' => $attributeId,
                    'component_code' => $attributeCode . '.1',
                    'component_name' => 'Component 1',
                    'component_description' => 'First component of ' . $attributeCode,
                    'max_points' => 50.0,
                    'data_type' => 'qualitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 1,
                    'status' => 'active'
                ],
                [
                    'attribute_id' => $attributeId,
                    'component_code' => $attributeCode . '.2',
                    'component_name' => 'Component 2',
                    'component_description' => 'Second component of ' . $attributeCode,
                    'max_points' => 50.0,
                    'data_type' => 'quantitative',
                    'applicable_to_schools' => 1,
                    'applicable_to_colleges' => 1,
                    'applicable_to_faculties' => 1,
                    'sort_order' => 2,
                    'status' => 'active'
                ]
            ];
    }
    
    return $subComponents;
}
